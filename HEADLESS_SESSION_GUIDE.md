# 🔒 Hướng dẫn xử lý vấn đề Session trong Headless Mode (UPDATED)

## 🚨 **Vấn đề**

Khi sử dụng API `chat-with-manus-realtime` với `headless=true`, profile đã login Manus bị đăng xuất. Trong khi đó, khi `headless=false` thì profile vẫn giữ được trạng thái đăng nhập.

## 🔍 **Nguyên nhân**

### 1. **Chrome Arguments xung đột**

Một số Chrome arguments có thể gây ra việc reset session:

- `--disable-extensions`: Vô hiệu hóa extensions có thể cần thiết cho session
- `--disable-web-security`: Ảnh hưởng đến cách cookies/session được lưu trữ
- `--disable-blink-features=AutomationControlled`: Có thể trigger anti-bot detection

### 2. **Headless Detection**

Một số website (bao gồ<PERSON>) có thể:

- Detect headless mode và reset session vì lý do bảo mật
- Sử dụng các API chỉ hoạt động trong non-headless mode
- Ki<PERSON>m tra user-agent và browser fingerprint

### 3. **Session Isolation**

Chrome trong headless mode có thể:

- Tạo ra phiên riêng biệt từ non-headless session
- Không chia sẻ cookies/localStorage giữa các mode
- Reset session khi detect automation

## ✅ **Giải pháp đã implement (UPDATED)**

### 1. **Stealth Headless Mode** ⭐ NEW

```python
# Mode stealth với args tối thiểu
await launch_browser_with_stealth_mode(
    profile_name="manus_login_profile",
    force_session_preservation=True
)
```

**Chrome Args được tối ưu:**

```python
chrome_args = [
    "--no-sandbox",
    "--disable-dev-shm-usage",
    "--no-first-run",
    "--disable-default-apps",
    "--no-zygote",
    "--single-process",
    "--disable-gpu",
    "--remote-debugging-port=0",
    "--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36"
]
```

### 2. **Session Backup/Restore System** ⭐ NEW

```python
# Trước khi chạy headless
await backup_session_data(profile_name)

# Sau khi chạy headless
await restore_session_data(profile_name)
```

**Backup các files quan trọng:**

- `Default/Cookies`
- `Default/Local Storage`
- `Default/Session Storage`
- `Default/IndexedDB`
- `Default/Preferences`

### 3. **Cookie Injection System** ⭐ NEW

```python
# Extract cookies từ profile
cookies = await extract_cookies_from_profile(profile_name)

# Inject vào headless context
await inject_cookies_to_context(context, profile_name)
```

### 4. **Enhanced Session Monitoring**

```python
"session_status": {
    "initially_logged_in": True,
    "still_logged_in_after_chat": is_still_logged_in,
    "profile_used": profile_name,
    "headless_mode": headless,
    "stealth_mode_used": headless,                    # NEW
    "session_backup_restored": headless and profile_name  # NEW
}
```

## 🧪 **Testing (UPDATED)**

### 1. **Comprehensive Test Suite**

```bash
# Test tất cả approaches
python test_all_approaches.py

# Debug chi tiết session files
python debug_session_issue.py

# Test cơ bản headless vs non-headless
python test_headless_session.py
```

### 2. **Expected Results**

```
📊 Test Results Summary
✅ Stealth Headless + Session Backup: Success=True, Session Preserved=True
✅ Cookie Injection + Headless: Success=True, Session Preserved=True
✅ Non-headless Baseline: Success=True, Session Preserved=True
```

## 🔧 **Cách sử dụng (UPDATED)**

### 1. **API Call với Enhanced Features**

```bash
curl -X POST "http://localhost:8000/chat-with-manus-realtime/" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Hello Manus!",
    "task_url": "https://manus.im/app/your-task-id",
    "profile_name": "manus_login_profile",
    "request_id": "test-123",
    "headless": true
  }'
```

### 2. **Enhanced Response**

```json
{
  "success": true,
  "chat_response": {
    "user_message": "Hello Manus!",
    "manus_response": "Hello! How can I help you?",
    "timestamp": "2024-01-15T10:30:00",
    "attachments": []
  },
  "session_status": {
    "initially_logged_in": true,
    "still_logged_in_after_chat": true,
    "profile_used": "manus_login_profile",
    "headless_mode": true,
    "stealth_mode_used": true,
    "session_backup_restored": true
  }
}
```

## 🔍 **Troubleshooting (UPDATED)**

### Approach 1: Stealth Mode

```python
# Nếu vẫn logout với stealth mode
await launch_browser_with_stealth_mode(
    profile_name="manus_login_profile",
    force_session_preservation=True
)
```

### Approach 2: Cookie Injection

```python
# Extract cookies trước
cookies = await extract_cookies_from_profile(profile_name)
# Sau đó inject vào headless context
```

### Approach 3: Session Backup/Restore

```python
# Manual backup/restore
await backup_session_data(profile_name)
# ... run headless ...
await restore_session_data(profile_name)
```

### Approach 4: Fallback to Non-headless

```python
# Nếu tất cả fail, fallback về non-headless
if not result["session_status"]["still_logged_in_after_chat"]:
    result = await chat_with_manus_interactive(..., headless=False)
```

## 📝 **Best Practices (UPDATED)**

1. **Test trước khi deploy production**
2. **Monitor session_status trong response**
3. **Implement fallback mechanism**
4. **Use comprehensive test suite để validate**
5. **Backup session data trước khi headless operations**
6. **Extract cookies định kỳ để maintain fresh session**

## 🚀 **Các tính năng mới**

- ✅ **Stealth headless mode** với minimal Chrome args
- ✅ **Automatic session backup/restore** system
- ✅ **Cookie extraction/injection** mechanism
- ✅ **Enhanced session monitoring** và reporting
- ✅ **Comprehensive testing suite** với multiple approaches
- ✅ **Detailed debugging tools** để investigate issues
- ✅ **Fallback mechanisms** cho robust operations

## 📁 **Files mới được tạo**

1. **`debug_session_issue.py`** - Debug session files và changes
2. **`test_all_approaches.py`** - Test tất cả approaches
3. **Enhanced `app/core/crawler.py`** - Stealth mode, backup/restore, cookie injection

---

**Recommended Workflow:**

1. Run `test_all_approaches.py` để xác định approach tốt nhất
2. Use API với `headless=true` với confidence
3. Monitor `session_status` trong response
4. Fallback to `headless=false` nếu cần thiết
5. Use `debug_session_issue.py` để investigate nếu có issues
