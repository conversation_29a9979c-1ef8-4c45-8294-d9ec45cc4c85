# 🚀 S3 Migration Guide - <PERSON><PERSON><PERSON><PERSON> <PERSON>ừ Cloudinary sang AWS S3

## 📋 Tổng quan

Hệ thống đã được update để chuyển từ việc upload file lên **Cloudinary** sang **AWS S3** với các tính năng:

- ✅ Upload file trực tiếp lên S3 với folder structure theo task ID
- ✅ Hỗ trợ giải nén và upload từng file trong ZIP
- ✅ Backward compatibility với Cloudinary (dữ liệu cũ vẫn hoạt động)
- ✅ Presigned URL cho upload từ frontend
- ✅ API endpoints mới cho quản lý S3

## 🔧 Cấu hình S3

### 1. Environment Variables (Backend)

Thêm các biến môi trường sau vào file `.env` hoặc system environment:

```bash
# AWS S3 Configuration
AWS_ACCESS_KEY_ID=your_access_key_here
AWS_SECRET_ACCESS_KEY=your_secret_key_here
AWS_REGION=us-east-1
S3_BUCKET_NAME=your_bucket_name_here
```

### 2. Frontend Environment Variables

Thêm vào file `.env` của React app:

```bash
# S3 Configuration for Frontend
REACT_APP_AWS_REGION=us-east-1
REACT_APP_S3_BUCKET_NAME=your_bucket_name_here
REACT_APP_AWS_ACCESS_KEY_ID=your_access_key_here
REACT_APP_AWS_SECRET_ACCESS_KEY=your_secret_key_here
```

## 🏗️ Cấu trúc file trên S3

Files được tổ chức theo cấu trúc:

```
s3://your-bucket/
├── crawl_results/          # Files từ crawler
│   └── {task_id}/
│       ├── file1.pdf
│       ├── file2.docx
│       └── extracted_zip/
│           ├── doc1.txt
│           └── subfolder/
│               └── nested.json
└── user_uploads/           # Files từ frontend
    └── {task_id}/
        ├── images/
        │   ├── photo1.jpg
        │   └── photo2.png
        ├── products/
        └── news/
```

## 🔄 Migration Steps

### 1. Cài đặt Dependencies

```bash
cd Youhome-2
pip install "boto3>=1.34.0"
```

### 2. Tạo S3 Bucket

1. Đăng nhập AWS Console
2. Tạo S3 bucket mới
3. Cấu hình CORS policy:

```json
[
  {
    "AllowedHeaders": ["*"],
    "AllowedMethods": ["GET", "POST", "PUT"],
    "AllowedOrigins": ["*"],
    "ExposeHeaders": ["ETag"]
  }
]
```

### 3. Tạo IAM User

Tạo IAM user với policy:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "s3:GetObject",
        "s3:PutObject",
        "s3:DeleteObject",
        "s3:ListBucket"
      ],
      "Resource": [
        "arn:aws:s3:::your-bucket-name",
        "arn:aws:s3:::your-bucket-name/*"
      ]
    }
  ]
}
```

## 📡 API Endpoints mới

### 1. Get Presigned URL

```http
POST /s3/get-presigned-url/
Content-Type: application/json

{
    "key": "images/photo.jpg",
    "content_type": "image/jpeg",
    "task_id": "task_123"
}
```

**Response:**

```json
{
  "success": true,
  "presigned_url": "https://bucket.s3.amazonaws.com/...",
  "fields": {
    "key": "user_uploads/task_123/images/photo.jpg",
    "Content-Type": "image/jpeg",
    "x-amz-meta-task_id": "task_123"
  }
}
```

### 2. List S3 Files

```http
GET /s3/list-files/{task_id}?prefix=images
```

**Response:**

```json
{
  "success": true,
  "task_id": "task_123",
  "files": [
    {
      "key": "user_uploads/task_123/images/photo.jpg",
      "size": 1024000,
      "last_modified": "2024-01-20T10:30:00",
      "url": "https://bucket.s3.amazonaws.com/..."
    }
  ],
  "total_files": 1
}
```

## 🎯 Frontend Usage

### 1. Upload file sử dụng S3Uploader

```javascript
import s3Uploader from "./utils/s3Upload";

// Upload single file
const taskId = s3Uploader.generateTaskId();
const result = await s3Uploader.uploadFile(file, taskId, "images");

if (result.success) {
  console.log("Upload successful:", result.s3_url);
  // Use result.s3_url thay vì cloudinary_url
} else {
  console.error("Upload failed:", result.error);
}

// Upload multiple files
const results = await s3Uploader.uploadMultipleFiles(
  files,
  taskId,
  "products",
  (progress) => {
    console.log(`Progress: ${progress.current}/${progress.total}`);
  }
);
```

### 2. Migration code từ Cloudinary

**Trước (Cloudinary):**

```javascript
const formData = new FormData();
formData.append("file", file);
formData.append("upload_preset", "AutoWork");
formData.append("api_key", "238276161954221");

const response = await axios.post(
  "https://api.cloudinary.com/v1_1/test-upload-1/image/upload",
  formData
);
const imageUrl = response.data.secure_url;
```

**Sau (S3):**

```javascript
const taskId = s3Uploader.generateTaskId();
const result = await s3Uploader.uploadFile(file, taskId, "images");
const imageUrl = result.s3_url;
```

### 3. AIPrice Component đã được cập nhật

**File:** `aiprice-fe/src/components/AIPrice/components/PriceAnalysisResult.js`

- ✅ Download files ưu tiên sử dụng `file.s3_url`, fallback về `file.cloudinary_url`
- ✅ Hiển thị nguồn lưu trữ (S3 Storage hoặc Cloudinary)
- ✅ Backward compatibility với files cũ từ Cloudinary
- ✅ Không cần thay đổi gì từ phía user

**Cách hoạt động:**

```javascript
// Tự động ưu tiên S3, fallback về Cloudinary
const downloadUrl = file.s3_url || file.cloudinary_url;

// Hiển thị nguồn lưu trữ cho user
const storageSource = file.s3_url ? "📁 S3 Storage" : "☁️ Cloudinary";
```

## 🔍 Monitoring và Debug

### 1. Kiểm tra S3 connection

```bash
# Check logs khi start server
python run.py

# Expected output:
# ✅ S3Uploader initialized with bucket: your-bucket-name
# hoặc
# ⚠️ S3 credentials not found. S3 upload will be disabled.
```

### 2. Test S3 endpoints

```bash
# Test presigned URL endpoint
curl -X POST http://localhost:8000/s3/get-presigned-url/ \
  -H "Content-Type: application/json" \
  -d '{
    "key": "test.jpg",
    "content_type": "image/jpeg",
    "task_id": "test_task"
  }'
```

## 🔄 Backward Compatibility

- ✅ File cũ từ Cloudinary vẫn hoạt động bình thường
- ✅ API responses bao gồm cả `cloudinary_url` và `s3_url`
- ✅ Frontend có thể handle cả 2 format
- ✅ Database schema không cần thay đổi

## 🚨 Troubleshooting

### 1. Server không start được

**Lỗi:** `ModuleNotFoundError: No module named 'boto3'`

**Giải pháp:**

```bash
cd Youhome-2
source venv/bin/activate
pip install "boto3>=1.34.0"
```

### 2. S3 upload failed

**Lỗi:** `S3 client not available`

**Giải pháp:**

1. Kiểm tra environment variables
2. Kiểm tra IAM permissions
3. Kiểm tra S3 bucket policy

### 3. CORS errors từ frontend

**Giải pháp:**

1. Thêm CORS policy vào S3 bucket
2. Kiểm tra allowed origins và methods

## 📈 Performance Notes

- ✅ S3 upload thường nhanh hơn Cloudinary
- ✅ Presigned URL giảm tải cho backend server
- ✅ Folder structure theo task ID giúp quản lý dễ dàng
- ✅ Automatic content type detection
- ✅ File size validation (max 100MB)

## 🔐 Security

- ✅ Presigned URL có thời gian hết hạn (1 hour default)
- ✅ Content type validation
- ✅ File size limits
- ✅ IAM permissions giới hạn theo bucket
- ✅ Metadata tracking (task_id, upload_time)

## 📝 Next Steps

1. **Setup monitoring:** CloudWatch metrics cho S3 usage
2. **Backup strategy:** Lifecycle policies cho old files
3. **CDN setup:** CloudFront cho faster delivery
4. **Cost optimization:** S3 storage classes cho archival
