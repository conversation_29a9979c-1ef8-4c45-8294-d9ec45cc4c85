# Docker Setup Guide

Hướng dẫn setup và chạy Manus Crawler với Docker, bao gồm Firebase integration.

## 🚀 Quick Start

### 1. Setup Firebase (Required)

```bash
# Run setup script
./docker/setup-firebase.sh

# Edit .env file with your Firebase settings
nano .env

# Add your Firebase credentials
# Replace ./data/firebase-credentials.json with your actual credentials
```

### 2. Build and Run

```bash
# Build and start containers
docker-compose up --build

# Or run in background
docker-compose up -d --build
```

### 3. Access Services

- **FastAPI**: http://localhost:8000
- **API Docs**: http://localhost:8000/docs
- **VNC (noVNC)**: http://localhost:6080/vnc.html
- **VNC Direct**: vnc://localhost:5900

## 📁 Directory Structure

```
.
├── docker/
│   ├── Dockerfile              # Main Docker image
│   ├── setup-firebase.sh       # Firebase setup script
│   └── healthcheck.sh          # Health check script
├── data/                       # Mounted data directory
│   ├── chrome_profiles/        # Chrome user profiles
│   ├── crawl_status/          # Crawl status files
│   └── firebase-credentials.json  # Firebase service account key
├── downloads/                  # Downloaded files
├── app/                        # Application source code
├── .env                        # Environment variables
└── docker-compose.yml         # Docker Compose configuration
```

## 🔥 Firebase Configuration

### 1. Get Firebase Credentials

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project
3. Go to **Project Settings** > **Service accounts**
4. Click **Generate new private key**
5. Save as `./data/firebase-credentials.json`

### 2. Setup Realtime Database

1. In Firebase Console, go to **Realtime Database**
2. Click **Create Database**
3. Choose location and security rules
4. Copy the database URL

### 3. Update Environment Variables

Edit `.env` file:

```env
# Firebase Settings
FIREBASE_CREDENTIALS_PATH=/app/data/firebase-credentials.json
FIREBASE_DATABASE_URL=https://your-project-id-default-rtdb.firebaseio.com/
```

## 🧪 Testing

### Test Firebase Connection

```bash
# Check Firebase status
curl http://localhost:8000/debug/firebase

# Expected response:
{
  "firebase_available": true,
  "test_write": true,
  "credentials_path": "/app/data/firebase-credentials.json",
  "database_url": "https://your-project-id-default-rtdb.firebaseio.com/",
  "credentials_exists": true
}
```

### Test API with Firebase Logging

```bash
# Test chat API with user_id
curl -X POST "http://localhost:8000/chat-with-manus-realtime/" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Test message",
    "task_url": "https://manus.im/app/test-task",
    "profile_name": "test_profile",
    "request_id": "test-request-123",
    "headless": true,
    "user_id": "docker_test_user"
  }'
```

### Check Firebase History

```bash
# Check user history
curl http://localhost:8000/debug/firebase-history/docker_test_user

# Check scheduler status
curl http://localhost:8000/debug/scheduler
```

## 🔧 Docker Commands

### Basic Operations

```bash
# Start services
docker-compose up

# Start in background
docker-compose up -d

# Stop services
docker-compose down

# Rebuild and start
docker-compose up --build

# View logs
docker-compose logs -f

# View logs for specific service
docker-compose logs -f web_crawler_app
```

### Development

```bash
# Rebuild only when Dockerfile changes
docker-compose build

# Force rebuild
docker-compose build --no-cache

# Shell into container
docker-compose exec web_crawler_app bash

# Check container status
docker-compose ps
```

### Debugging

```bash
# Check health status
docker-compose exec web_crawler_app /app/healthcheck.sh

# View container logs
docker logs manus_crawler_service

# Check Firebase connection inside container
docker-compose exec web_crawler_app curl http://localhost:8000/debug/firebase
```

## 📊 Monitoring

### Health Checks

The container includes automatic health checks:

- **FastAPI**: Checks if API is responding
- **Firebase**: Checks if Firebase is configured and accessible
- **Interval**: Every 30 seconds
- **Timeout**: 15 seconds
- **Retries**: 3 attempts

### Logs

```bash
# Application logs
docker-compose logs web_crawler_app

# VNC logs
docker-compose exec web_crawler_app cat /home/<USER>/x11vnc.log

# Xvfb logs
docker-compose exec web_crawler_app cat /home/<USER>/xvfb.log
```

## 🚨 Troubleshooting

### Firebase Issues

1. **"Firebase not available"**
   - Check if `firebase-credentials.json` exists in `./data/`
   - Verify credentials file format
   - Check `FIREBASE_DATABASE_URL` in `.env`

2. **"Permission denied"**
   - Check Firebase security rules
   - Verify service account permissions

3. **"Database URL not found"**
   - Ensure `FIREBASE_DATABASE_URL` is set correctly
   - Format: `https://project-id-default-rtdb.firebaseio.com/`

### Container Issues

1. **Container won't start**
   - Check Docker logs: `docker-compose logs`
   - Verify all required files exist
   - Check port conflicts

2. **VNC not accessible**
   - Ensure port 6080 is not in use
   - Check firewall settings
   - Try direct VNC on port 5900

3. **API not responding**
   - Check if port 8000 is available
   - Verify health check: `curl http://localhost:8000/health`
   - Check application logs

## 🔄 Updates

When updating the application:

1. **Code changes**: Automatically reflected (volume mounted)
2. **Dependencies**: Rebuild container: `docker-compose up --build`
3. **Docker config**: Rebuild: `docker-compose build --no-cache`

## 📋 Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `FIREBASE_CREDENTIALS_PATH` | Path to Firebase credentials | `/app/data/firebase-credentials.json` |
| `FIREBASE_DATABASE_URL` | Firebase Realtime Database URL | - |
| `CHROME_PROFILE_BASE_PATH` | Chrome profiles directory | `/app/data/chrome_profiles` |
| `PYTHONUNBUFFERED` | Python output buffering | `1` |
| `DISPLAY` | X11 display | `:99` |
| `VNC_PASSWORD` | VNC password | `admin123` |
| `NOVNC_PORT` | noVNC web port | `6080` |
