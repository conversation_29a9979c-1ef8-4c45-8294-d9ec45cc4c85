#!/usr/bin/env python3
"""
Test check status logic với 2 trường hợp:
1. Logic cũ - completion message
2. Logic mới - copy button
"""

import requests
import json
import time

def test_check_status_api():
    """Test check-crawl-status API"""
    
    url = "http://localhost:8000/check-crawl-status/"
    
    # Test với task ID giả
    payload = {
        "task_id": "test_task_" + str(int(time.time())),
        "profile_name": "test_profile",
        "headless": True
    }
    
    print("🔍 Testing check-crawl-status API...")
    print(f"📤 Payload: {json.dumps(payload, indent=2)}")
    
    try:
        response = requests.post(url, json=payload, timeout=60)
        
        print(f"📥 Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"📥 Response: {json.dumps(data, indent=2)}")
            
            # Kiểm tra detection_method
            detection_method = data.get("detection_method")
            if detection_method:
                print(f"✅ Detection method: {detection_method}")
                
                if detection_method == "completion_message":
                    print("🎯 Detected completion using completion message (old logic)")
                elif detection_method == "copy_button":
                    print("🎯 Detected completion using copy button (new logic)")
                elif detection_method == "none":
                    print("⏳ Task not completed yet")
                else:
                    print(f"❓ Unknown detection method: {detection_method}")
            
            return True
        else:
            print(f"❌ API call failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error calling API: {str(e)}")
        return False

def test_selectors():
    """Test selectors để đảm bảo chúng được load đúng"""
    
    print("\n🔧 Testing selectors...")
    
    try:
        import sys
        from pathlib import Path
        sys.path.insert(0, str(Path(__file__).parent))
        
        from app.core.selectors import MAKE_COMPLETED_CSS, COPY_BUTTON_CSS
        
        print(f"✅ MAKE_COMPLETED_CSS: {MAKE_COMPLETED_CSS}")
        print(f"✅ COPY_BUTTON_CSS: {COPY_BUTTON_CSS}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error loading selectors: {str(e)}")
        return False

def show_selector_info():
    """Hiển thị thông tin về selectors"""
    
    print("\n📋 Selector Information:")
    print("=" * 50)
    
    print("🎯 Trường hợp 1 - Completion Message:")
    print("   Selector: //div[contains(@class, 'rounded-full') and contains(normalize-space(.), 'Manus has completed the current task')]")
    print("   Mô tả: Tìm element chứa text 'Manus has completed the current task'")
    
    print("\n🎯 Trường hợp 2 - Copy Button:")
    print("   Selector: div.flex.h-7.w-7.items-center.justify-center.cursor-pointer svg.lucide-copy")
    print("   Mô tả: Tìm SVG copy icon trong button")
    print("   HTML mẫu:")
    print("   <div class=\"flex h-7 w-7 items-center justify-center cursor-pointer hover:bg-[var(--fill-tsp-gray-main)] rounded-md\">")
    print("     <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"lucide lucide-copy size-5 w-4 h-4 text-[var(--icon-secondary)]\">")
    print("       <rect width=\"14\" height=\"14\" x=\"8\" y=\"8\" rx=\"2\" ry=\"2\"></rect>")
    print("       <path d=\"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2\"></path>")
    print("     </svg>")
    print("   </div>")

if __name__ == "__main__":
    print("🧪 Testing Check Status Logic...\n")
    
    # Test selectors
    selectors_ok = test_selectors()
    
    if selectors_ok:
        # Show selector info
        show_selector_info()
        
        # Test API
        print("\n" + "="*50)
        api_ok = test_check_status_api()
        
        if api_ok:
            print("\n✅ Check status test completed!")
        else:
            print("\n❌ Check status test failed!")
    else:
        print("\n❌ Selector test failed!")
    
    print("\n📋 Next steps:")
    print("1. Kiểm tra server logs để xem detection method")
    print("2. Test với task thực tế để xem logic hoạt động")
    print("3. Kiểm tra Firebase để xem có log không")
