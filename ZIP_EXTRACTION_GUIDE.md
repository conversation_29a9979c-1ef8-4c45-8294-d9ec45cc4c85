# 📦 ZIP Extraction & Upload Guide

Hướng dẫn sử dụng chức năng mới: **Tự động giải nén file ZIP và upload từng file lên Cloudinary**.

## 🎯 Tính năng mới

Khi hệ thống phát hiện file ZIP từ Manus.im, nó sẽ:

1. **Download file ZIP** về local
2. **Tự động detect** đây là file ZIP
3. **Giải nén toàn bộ** files trong ZIP
4. **Upload từng file riêng lẻ** lên Cloudinary
5. **Tổ chức file** theo cấu trúc thư mục gốc
6. **Báo cáo chi tiết** về quá trình xử lý

## 🔧 Cách hoạt động

### 1. ZIP Detection

```python
# Kiểm tra file extension
if file.endswith('.zip'):
    # Kiểm tra magic number để đảm bảo
    if file_header == b'\x50\x4b\x03\x04':
        return True
```

### 2. Extraction Process

```python
# Giải nén vào thư mục tạm
with zipfile.ZipFile(zip_path, 'r') as zip_ref:
    zip_ref.extractall(temp_extract_dir)

# Upload từng file
for root, dirs, files in os.walk(temp_extract_dir):
    for file in files:
        upload_result = await upload_file(file_path, task_id, subfolder)
```

### 3. Cloudinary Organization

Files được tổ chức trên Cloudinary theo cấu trúc:

```
cloudinary://crawl_results/
└── {task_id}/
    └── {zip_filename}/
        ├── file1.txt
        ├── file2.py
        └── subfolder/
            └── nested_file.json
```

## 📊 Response Structure

### Khi ZIP được giải nén thành công:

```json
{
  "type": "zip_extracted",
  "zip_filename": "project_files.zip",
  "total_extracted": 5,
  "extracted_files": [
    {
      "filename": "readme.txt",
      "cloudinary_url": "https://res.cloudinary.com/...",
      "subfolder": "project_files",
      "extracted_from_zip": "project_files.zip",
      "relative_path_in_zip": "readme.txt"
    },
    {
      "filename": "config.json",
      "cloudinary_url": "https://res.cloudinary.com/...",
      "subfolder": "project_files/config",
      "extracted_from_zip": "project_files.zip",
      "relative_path_in_zip": "config/config.json"
    }
  ],
  "success_message": "Đã giải nén và upload 5 files từ project_files.zip"
}
```

### Khi giải nén thất bại (fallback):

```json
{
  "type": "zip_original",
  "filename": "project_files.zip",
  "cloudinary_url": "https://res.cloudinary.com/...",
  "extraction_failed": true
}
```

### File thường (không phải ZIP):

```json
{
  "type": "regular_file",
  "filename": "document.pdf",
  "cloudinary_url": "https://res.cloudinary.com/..."
}
```

## 🧪 Testing

Chạy test để demo chức năng:

```bash
python test_zip_extraction.py
```

### Expected Output:

```
🚀 Starting ZIP extraction tests...
============================================================

🧪 Testing single file upload for comparison...
✅ Single file upload successful:
   📄 Filename: test_single_file.txt
   🔗 URL: https://res.cloudinary.com/test-upload-1/...

============================================================

🧪 Testing ZIP extraction and upload to Cloudinary...
✅ Created test zip file: test_files.zip

🔍 Testing zip detection...
Is zip file: True

📦 Testing extract and upload...
📦 Giải nén zip file: test_files.zip -> /tmp/extract_test_...
  ✅ Uploaded extracted file: readme.txt
  ✅ Uploaded extracted file: config.json
  ✅ Uploaded extracted file: script.py
  ✅ Uploaded extracted file: data.csv
  ✅ Uploaded extracted file: deep.md
✅ Hoàn thành giải nén và upload 5 files từ zip

✅ Successfully extracted and uploaded 5 files:
  1. readme.txt
     📁 Subfolder: test_files
     🔗 URL: https://res.cloudinary.com/...
     📂 Original zip: test_files.zip
     📍 Path in zip: readme.txt

  2. config.json
     📁 Subfolder: test_files
     ...
```

## 🎯 Use Cases

### 1. **Code Projects từ Manus**

Khi Manus generate và compress một project thành ZIP:

- ✅ Tất cả files (.py, .js, .html, etc.) được extract riêng lẻ
- ✅ Cấu trúc thư mục được preserve
- ✅ Dễ dàng view và download từng file

### 2. **Document Collections**

Khi nhận ZIP chứa nhiều documents:

- ✅ Mỗi document có URL riêng trên Cloudinary
- ✅ Có thể share link từng file cụ thể
- ✅ Search và index tốt hơn

### 3. **Asset Packages**

Khi download images, fonts, hoặc assets:

- ✅ Từng asset có URL accessible riêng
- ✅ Tổ chức theo subfolder rõ ràng
- ✅ Easy integration với other systems

## ⚠️ Considerations

### File Size Limits

- **Cloudinary free tier**: 10MB per file
- **ZIP size**: Không giới hạn, nhưng tổng file size extracted không nên vượt quá storage limit

### Security

- ✅ **Zip validation**: Kiểm tra magic number
- ✅ **Path traversal protection**: Không extract file ra ngoài thư mục target
- ✅ **Temp cleanup**: Tự động dọn dẹp files tạm sau khi upload

### Performance

- ⚡ **Parallel upload**: Upload từng file song song (có thể cải thiện)
- 🧹 **Memory efficient**: Xử lý từng file một, không load toàn bộ vào memory
- 📁 **Storage optimized**: Dọn dẹp temp files ngay sau khi upload

## 🔄 Fallback Behavior

Nếu bất kỳ bước nào trong quá trình giải nén thất bại:

1. **System logs error** chi tiết
2. **Falls back** to upload ZIP file nguyên bản
3. **Returns response** với `extraction_failed: true`
4. **User vẫn có access** to original ZIP file

## 📝 Next Steps

### Potential Improvements:

- [ ] **Parallel upload** extracted files
- [ ] **Progress tracking** cho từng file trong ZIP
- [ ] **Selective extraction** (only certain file types)
- [ ] **Size limits** per extracted file
- [ ] **Virus scanning** cho extracted files
- [ ] **Metadata extraction** (file info, thumbnails)

---

**🎉 Ready to use!** Chức năng ZIP extraction đã sẵn sàng và tích hợp hoàn toàn vào hệ thống crawling hiện tại.
