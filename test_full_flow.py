#!/usr/bin/env python3
"""
Test full flow:
1. Call chat-with-manus-realtime với user_id
2. Kiểm tra Firebase history được tạo
3. Kiểm tra scheduler được khởi động
4. Monitor scheduler status
"""

import requests
import json
import time
import asyncio

def test_chat_api_with_scheduler():
    """Test chat API và scheduler"""

    url = "http://localhost:8000/chat-with-manus-realtime/"

    # Tạo unique IDs
    timestamp = str(int(time.time()))

    payload = {
        "message": "Test message for scheduler",
        "task_url": f"https://manus.im/app/test-task-{timestamp}",
        "profile_name": "test_profile",
        "request_id": f"test-request-{timestamp}",
        "headless": True,
        "user_id": f"scheduler_test_user_{timestamp}"
    }

    print("🚀 Testing chat API with scheduler...")
    print(f"📤 Payload: {json.dumps(payload, indent=2)}")

    try:
        response = requests.post(url, json=payload, timeout=30)

        print(f"📥 Status Code: {response.status_code}")
        print(f"📥 Response: {response.text}")

        if response.status_code == 200:
            data = response.json()
            task_id = data.get("task_id")
            user_id = data.get("user_id")

            print(f"✅ API call successful!")
            print(f"📋 Task ID: {task_id}")
            print(f"👤 User ID: {user_id}")

            return task_id, user_id
        else:
            print("❌ API call failed!")
            return None, None

    except Exception as e:
        print(f"❌ Error calling API: {str(e)}")
        return None, None

def check_scheduler_status():
    """Kiểm tra trạng thái scheduler"""

    url = "http://localhost:8000/debug/scheduler"

    print("\n🔍 Checking scheduler status...")

    try:
        response = requests.get(url, timeout=10)

        print(f"📥 Status Code: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print(f"📥 Scheduler Status: {json.dumps(data, indent=2)}")

            scheduler_status = data.get("scheduler_status", {})
            total_tasks = scheduler_status.get("total_tasks", 0)
            scheduler_running = scheduler_status.get("scheduler_running", False)

            print(f"🏃 Scheduler running: {scheduler_running}")
            print(f"📊 Total tasks monitoring: {total_tasks}")

            if total_tasks > 0:
                tasks = scheduler_status.get("tasks", {})
                for task_id, task_info in tasks.items():
                    print(f"  📋 Task {task_id}:")
                    print(f"    👤 User: {task_info.get('user_id')}")
                    print(f"    🔢 Check count: {task_info.get('check_count')}/{task_info.get('max_checks')}")
                    print(f"    ⏰ Last check: {task_info.get('last_check')}")

            return scheduler_running, total_tasks
        else:
            print(f"❌ Scheduler status check failed: {response.text}")
            return False, 0

    except Exception as e:
        print(f"❌ Error checking scheduler: {str(e)}")
        return False, 0

def monitor_scheduler(duration_minutes=10):
    """Monitor scheduler trong một khoảng thời gian"""

    print(f"\n⏰ Monitoring scheduler for {duration_minutes} minutes...")
    print("📋 Expected flow:")
    print("  1. doing → completed (check status)")
    print("  2. completed → crawling (start crawl)")
    print("  3. crawling → uploading (during upload)")
    print("  4. uploading → uploaded (final)")

    start_time = time.time()
    end_time = start_time + (duration_minutes * 60)
    check_interval = 30  # Check mỗi 30 giây

    last_status = {}

    while time.time() < end_time:
        print(f"\n--- Check at {time.strftime('%H:%M:%S')} ---")

        running, total_tasks = check_scheduler_status()

        # Track status changes
        current_status = get_current_task_status()
        if current_status != last_status:
            print("📊 Status changes detected:")
            for task_id, status in current_status.items():
                old_status = last_status.get(task_id, "unknown")
                if status != old_status:
                    print(f"  📋 Task {task_id}: {old_status} → {status}")
            last_status = current_status

        if not running and total_tasks == 0:
            print("🛑 Scheduler stopped and no tasks remaining")
            break

        print(f"⏳ Waiting {check_interval} seconds...")
        time.sleep(check_interval)

    print("\n✅ Monitoring completed")

def get_current_task_status():
    """Lấy status hiện tại của tất cả tasks"""
    try:
        response = requests.get("http://localhost:8000/debug/scheduler", timeout=5)
        if response.status_code == 200:
            data = response.json()
            tasks = data.get("scheduler_status", {}).get("tasks", {})
            return {task_id: info.get("status", "unknown") for task_id, info in tasks.items()}
    except:
        pass
    return {}

def check_firebase_history(user_id):
    """Kiểm tra Firebase history của user"""

    url = f"http://localhost:8000/debug/firebase-history/{user_id}"

    print(f"\n🔥 Checking Firebase history for user: {user_id}")

    try:
        response = requests.get(url, timeout=10)

        if response.status_code == 200:
            data = response.json()

            if data.get("success"):
                tasks = data.get("tasks", {})
                total_tasks = data.get("total_tasks", 0)

                print(f"📊 Found {total_tasks} task(s) in Firebase history")

                for task_id, task_info in tasks.items():
                    status = task_info.get("status", "unknown")
                    created_at = task_info.get("created_at", "")
                    updated_at = task_info.get("updated_at", "")
                    detection_method = task_info.get("detection_method", "")

                    print(f"  📋 Task {task_id}:")
                    print(f"    📊 Status: {status}")
                    print(f"    🕐 Created: {created_at}")
                    print(f"    🕑 Updated: {updated_at}")
                    if detection_method:
                        print(f"    🎯 Detection: {detection_method}")

                return tasks
            else:
                print(f"❌ Firebase history check failed: {data.get('error', 'Unknown error')}")
                return {}
        else:
            print(f"❌ Firebase history API failed: {response.text}")
            return {}

    except Exception as e:
        print(f"❌ Error checking Firebase history: {str(e)}")
        return {}

def test_firebase_debug():
    """Test Firebase debug endpoint"""

    url = "http://localhost:8000/debug/firebase"

    print("\n🔥 Testing Firebase debug...")

    try:
        response = requests.get(url, timeout=10)

        if response.status_code == 200:
            data = response.json()
            firebase_available = data.get("firebase_available", False)
            test_write = data.get("test_write", False)

            print(f"🔥 Firebase available: {firebase_available}")
            print(f"✍️ Test write: {test_write}")

            return firebase_available and test_write
        else:
            print(f"❌ Firebase debug failed: {response.text}")
            return False

    except Exception as e:
        print(f"❌ Error testing Firebase: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 Testing Full Flow: Chat API + Firebase + Scheduler\n")

    # 1. Test Firebase
    firebase_ok = test_firebase_debug()

    if not firebase_ok:
        print("❌ Firebase not working. Stopping test.")
        exit(1)

    # 2. Test chat API
    task_id, user_id = test_chat_api_with_scheduler()

    if not task_id:
        print("❌ Chat API failed. Stopping test.")
        exit(1)

    # 3. Check scheduler immediately
    time.sleep(2)  # Đợi scheduler khởi động
    running, total_tasks = check_scheduler_status()

    if running and total_tasks > 0:
        print(f"✅ Scheduler started successfully with {total_tasks} task(s)")

        # 4. Check Firebase history
        if user_id:
            check_firebase_history(user_id)

        # 5. Monitor scheduler
        monitor_scheduler(duration_minutes=3)  # Monitor 3 phút

        # 6. Final Firebase history check
        if user_id:
            print("\n🔍 Final Firebase history check:")
            final_history = check_firebase_history(user_id)

            # Kiểm tra xem có task nào đã hoàn thành flow không
            for task_id, task_info in final_history.items():
                status = task_info.get("status", "unknown")
                if status == "uploaded":
                    print(f"🎉 Task {task_id} completed full flow: doing → completed → crawling → uploading → uploaded")
                elif status in ["crawling", "uploading"]:
                    print(f"⏳ Task {task_id} in progress: {status}")
                elif status == "completed":
                    print(f"✅ Task {task_id} completed check, waiting for crawl")
    else:
        print("❌ Scheduler not started or no tasks found")

    print("\n📋 Test Summary:")
    print("1. ✅ Firebase connection tested")
    print("2. ✅ Chat API called with user_id")
    print("3. ✅ Task added to scheduler")
    print("4. ✅ Scheduler monitoring tested")
    print("\n🔄 Expected Flow:")
    print("  📋 doing → completed → crawling → uploading → uploaded")
    print("\n🎯 Next steps:")
    print("1. Check Firebase console for status updates")
    print("2. Monitor scheduler logs for status transitions")
    print("3. Wait for full flow completion (may take 5-10 minutes)")
    print("4. Check final 'uploaded' status with crawl data")
