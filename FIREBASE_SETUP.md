# Firebase Setup Guide

Hướng dẫn cấu hình Firebase Realtime Database để lưu lịch sử API calls.

## 1. Tạo Firebase Project

1. T<PERSON>y cập [Firebase Console](https://console.firebase.google.com/)
2. Tạo project mới hoặc chọn project có sẵn
3. Vào **Project Settings** > **Service accounts**
4. Click **Generate new private key** để tải file JSON credentials

## 2. Cấu hình Realtime Database

1. Trong Firebase Console, vào **Realtime Database**
2. Click **Create Database**
3. Chọn location (ví dụ: `us-central1`)
4. Chọn **Start in test mode** (có thể thay đổi rules sau)

## 3. Cấu hình Security Rules

Vào **Realtime Database** > **Rules** và cập nhật:

```json
{
  "rules": {
    "api_history": {
      ".read": "auth != null",
      ".write": "auth != null"
    },
    "user_history": {
      "$user_id": {
        ".read": "auth != null && auth.uid == $user_id",
        ".write": "auth != null && auth.uid == $user_id"
      }
    },
    "histories": {
      "$user_id": {
        ".read": "auth != null",
        ".write": "auth != null"
      }
    }
  }
}
```

## 4. Cấu hình Environment Variables

1. Copy file `.env.example` thành `.env`:
   ```bash
   cp .env.example .env
   ```

2. Cập nhật các biến Firebase trong `.env`:
   ```env
   FIREBASE_CREDENTIALS_PATH=./data/firebase-credentials.json
   FIREBASE_DATABASE_URL=https://your-project-id-default-rtdb.firebaseio.com/
   ```

3. Đặt file credentials JSON vào `./data/firebase-credentials.json`

## 5. Cấu trúc dữ liệu Firebase

### API History Logs
```
api_history/
  2024-01-15/
    1705123456789/
      timestamp: "2024-01-15T10:30:56.789Z"
      endpoint: "/chat-with-manus-realtime/"
      method: "POST"
      user_id: "user123"
      status_code: 200
      execution_time: 2.5
      request_data: {...}
      response_data: {...}
```

### User Task History
```
histories/
  user123/
    task_abc123/
      status: "doing"
      created_at: "2024-01-15T10:30:56.789Z"
      updated_at: "2024-01-15T10:30:56.789Z"
      message: "Hello Manus..."
      task_url: "https://manus.im/app/task-id"
      profile_name: "manus_login_profile"
      request_id: "uuid-here"
```

## 6. Cài đặt Dependencies

```bash
pip install firebase-admin>=6.2.0
```

## 7. Test Firebase Connection

Sau khi cấu hình xong, khởi động server và gọi API:

```bash
python run.py
```

Kiểm tra logs để xem Firebase có kết nối thành công:
- `✅ Firebase app initialized successfully`
- `✅ Saved task history: user=user123, task=task_abc123, status=doing`

## 8. Troubleshooting

### Lỗi "Firebase credentials not configured"
- Kiểm tra file `.env` có đúng path đến credentials file
- Kiểm tra file credentials JSON có tồn tại

### Lỗi "Permission denied"
- Kiểm tra Security Rules trong Firebase Console
- Đảm bảo service account có quyền truy cập database

### Lỗi "Database URL not found"
- Kiểm tra `FIREBASE_DATABASE_URL` trong `.env`
- URL phải có format: `https://project-id-default-rtdb.firebaseio.com/`

## 9. Monitoring

Bạn có thể xem dữ liệu realtime trong Firebase Console:
- **Realtime Database** > **Data** để xem cấu trúc dữ liệu
- **Usage** để xem thống kê sử dụng
