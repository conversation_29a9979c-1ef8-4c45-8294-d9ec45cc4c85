#!/usr/bin/env python3
"""
Script test Firebase connection
"""

import asyncio
import sys
import os
from pathlib import Path

# Thêm app vào Python path
sys.path.insert(0, str(Path(__file__).parent))

from app.utils.firebase_service import firebase_service
from app.core.config import settings

async def test_firebase():
    """Test Firebase connection và ghi dữ liệu"""
    
    print("🔥 Testing Firebase Connection...")
    print(f"📁 Credentials path: {settings.FIREBASE_CREDENTIALS_PATH}")
    print(f"🌐 Database URL: {settings.FIREBASE_DATABASE_URL}")
    print(f"📄 Credentials file exists: {os.path.exists(settings.FIREBASE_CREDENTIALS_PATH) if settings.FIREBASE_CREDENTIALS_PATH else False}")
    
    # Khởi tạo Firebase
    firebase_service.initialize()
    
    # Kiểm tra connection
    is_available = firebase_service.is_available()
    print(f"✅ Firebase available: {is_available}")
    
    if not is_available:
        print("❌ Firebase not available. Check your configuration.")
        return
    
    # Test ghi API log
    print("\n📝 Testing API log...")
    api_log_result = await firebase_service.log_api_call(
        endpoint="/test-endpoint",
        method="POST",
        user_id="test_user_123",
        request_data={"test": "data"},
        response_data={"success": True},
        status_code=200
    )
    print(f"API log result: {api_log_result}")
    
    # Test ghi user task history
    print("\n📋 Testing user task history...")
    task_history_result = await firebase_service.save_user_task_history(
        user_id="test_user_123",
        task_id="test_task_456",
        status="doing",
        task_data={
            "message": "Test message from script",
            "task_url": "https://test.com/task",
            "profile_name": "test_profile"
        }
    )
    print(f"Task history result: {task_history_result}")
    
    # Test cập nhật status
    print("\n🔄 Testing status update...")
    update_result = await firebase_service.update_user_task_status(
        user_id="test_user_123",
        task_id="test_task_456",
        status="completed",
        additional_data={"completed_at": firebase_service._get_current_timestamp()}
    )
    print(f"Status update result: {update_result}")
    
    print("\n✅ Firebase test completed!")

if __name__ == "__main__":
    asyncio.run(test_firebase())
