#!/usr/bin/env python3
"""
Test script cho S3 upload với non-ASCII subfolder
"""

import asyncio
import tempfile
import os
from app.utils.s3_upload import s3_uploader

async def test_s3_upload_non_ascii():
    """Test upload file với subfolder chứa ký tự tiếng Việt"""
    
    # Tạo file test tạm thời
    test_content = "Đây là nội dung test với tiếng Việt 🇻🇳"
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
        f.write(test_content)
        temp_file_path = f.name
    
    try:
        print(f"📄 Tạo file test: {temp_file_path}")
        
        # Test với subfolder tiếng Việt
        test_cases = [
            {
                "subfolder": "Tạo file PDF tổng hợp giá vàng hôm qua",
                "task_id": "test_vietnamese_folder",
                "description": "Subfolder tiếng Việt"
            },
            {
                "subfolder": "Tài liệu/Báo cáo/2024",
                "task_id": "test_nested_vietnamese",
                "description": "Nested subfolder với tiếng Việt"
            },
            {
                "subfolder": None,
                "task_id": "test_no_subfolder",
                "description": "Không có subfolder"
            }
        ]
        
        for test_case in test_cases:
            print(f"\n🧪 Test case: {test_case['description']}")
            print(f"   Subfolder: {test_case['subfolder']}")
            print(f"   Task ID: {test_case['task_id']}")
            
            # Upload file
            result = await s3_uploader.upload_file(
                file_path=temp_file_path,
                task_id=test_case['task_id'],
                subfolder=test_case['subfolder']
            )
            
            if result:
                print(f"   ✅ Upload thành công!")
                print(f"   📍 S3 URL: {result['s3_url']}")
                print(f"   🔑 S3 Key: {result['s3_key']}")
                print(f"   📁 Subfolder in metadata: {result.get('subfolder', 'N/A')}")
            else:
                print(f"   ❌ Upload thất bại!")
    
    finally:
        # Dọn dẹp file tạm
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)
            print(f"\n🧹 Đã xóa file test: {temp_file_path}")

async def test_s3_upload_bytes():
    """Test upload từ bytes với subfolder tiếng Việt"""
    
    print(f"\n🧪 Test upload từ bytes với subfolder tiếng Việt")
    
    # Tạo content test
    test_content = "Nội dung test từ bytes với ký tự đặc biệt: àáạảãâầấậẩẫăằắặẳẵ".encode('utf-8')
    
    result = await s3_uploader.upload_file_bytes(
        file_bytes=test_content,
        filename="test-vietnamese-bytes.txt",
        task_id="test_bytes_upload",
        subfolder="Thư mục test bytes với tiếng Việt",
        content_type="text/plain"
    )
    
    if result:
        print(f"   ✅ Upload bytes thành công!")
        print(f"   📍 S3 URL: {result['s3_url']}")
        print(f"   🔑 S3 Key: {result['s3_key']}")
        print(f"   📁 Subfolder: {result.get('subfolder', 'N/A')}")
    else:
        print(f"   ❌ Upload bytes thất bại!")

def main():
    """Main function để chạy tests"""
    print("🚀 Bắt đầu test S3 upload với non-ASCII subfolder")
    print("=" * 60)
    
    # Kiểm tra S3 có sẵn sàng không
    if not s3_uploader._check_s3_available():
        print("❌ S3 client không sẵn sàng. Kiểm tra cấu hình AWS credentials.")
        return
    
    print(f"✅ S3 client sẵn sàng. Bucket: {s3_uploader.bucket_name}")
    
    # Chạy tests
    asyncio.run(test_s3_upload_non_ascii())
    asyncio.run(test_s3_upload_bytes())
    
    print("\n" + "=" * 60)
    print("🎉 Hoàn thành tất cả tests!")

if __name__ == "__main__":
    main() 