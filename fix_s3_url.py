#!/usr/bin/env python3
"""
Script để khắc phục URL S3 bị lỗi do encoding ký tự tiếng Việt
"""

import sys
from urllib.parse import unquote
from app.utils.s3_upload import s3_uploader

def analyze_broken_url(broken_url: str):
    """
    Phân tích URL bị lỗi và tìm ra nguyên nhân
    """
    print("🔍 PHÂN TÍCH URL BỊ LỖI")
    print("=" * 50)
    print(f"URL gốc: {broken_url}")
    
    # Decode URL encoding để xem nội dung thực
    import re
    url_pattern = r'https://([^/]+)\.s3\.([^/]+)\.amazonaws\.com/(.+)'
    match = re.match(url_pattern, broken_url)
    
    if match:
        bucket_name = match.group(1)
        region = match.group(2)
        s3_path = match.group(3)
        
        print(f"📦 Bucket: {bucket_name}")
        print(f"🌍 Region: {region}")
        print(f"🔗 S3 Path (encoded): {s3_path}")
        
        # Decode path
        decoded_path = unquote(s3_path)
        print(f"📄 S3 Path (decoded): {decoded_path}")
        
        # Phân tích các thành phần
        path_parts = decoded_path.split('/')
        print(f"📁 Path components:")
        for i, part in enumerate(path_parts):
            print(f"   {i+1}. {part}")
            
        return {
            'bucket': bucket_name,
            'region': region,
            'path_parts': path_parts,
            'decoded_path': decoded_path
        }
    else:
        print("❌ URL không đúng định dạng S3")
        return None

def fix_url(broken_url: str):
    """
    Khắc phục URL bị lỗi
    """
    print("\n🔧 KHẮC PHỤC URL")
    print("=" * 50)
    
    # Sử dụng function fix_s3_url_encoding từ S3Uploader
    fixed_url = s3_uploader.fix_s3_url_encoding(broken_url)
    
    print(f"\n✅ URL đã sửa: {fixed_url}")
    return fixed_url

def create_new_proper_url(task_id: str, subfolder: str, filename: str):
    """
    Tạo URL mới đúng cách từ thông tin gốc
    """
    print("\n🆕 TẠO URL MỚI ĐÚNG CÁCH")
    print("=" * 50)
    
    # Tạo URL mới với proper sanitization
    new_url = s3_uploader.create_proper_s3_url(task_id, subfolder, filename)
    
    if new_url:
        print(f"\n🌐 URL mới: {new_url}")
        return new_url
    else:
        print("❌ Không thể tạo URL mới")
        return None

def main():
    """
    Main function
    """
    print("🚀 SCRIPT KHẮC PHỤC URL S3 BỊ LỖI")
    print("=" * 60)
    
    # URL bị lỗi từ user
    broken_url = "https://storage-file-s3-v1.s3.us-east-1.amazonaws.com/crawl_results/4vwIS7665INBoQ2Y1UMOWt/T%E1%BB%95ng%20h%E1%BB%A3p%20gi%C3%A1%20v%C3%A0ng%20h%C3%B4m%20nay%20pdf/gold_price_report.pdf"
    
    # Phân tích URL
    analysis = analyze_broken_url(broken_url)
    
    if analysis:
        # Khắc phục URL
        fixed_url = fix_url(broken_url)
        
        # Tạo URL mới đúng cách
        if len(analysis['path_parts']) >= 3:
            task_id = analysis['path_parts'][1]  # crawl_results/{task_id}/...
            subfolder = analysis['path_parts'][2]  # subfolder
            filename = analysis['path_parts'][3] if len(analysis['path_parts']) > 3 else "unknown_file"
            
            print(f"\n📋 THÔNG TIN TRÍCH XUẤT:")
            print(f"   Task ID: {task_id}")
            print(f"   Subfolder: {subfolder}")
            print(f"   Filename: {filename}")
            
            new_url = create_new_proper_url(task_id, subfolder, filename)
        
        print("\n" + "=" * 60)
        print("📝 TÓM TẮT KẾT QUẢ:")
        print(f"🔴 URL gốc (bị lỗi): {broken_url}")
        print(f"🟡 URL đã fix: {fixed_url}")
        if 'new_url' in locals():
            print(f"🟢 URL mới (đúng cách): {new_url}")
            
        print("\n💡 KHUYẾN NGHỊ:")
        print("- Sử dụng URL mới (màu xanh) cho các file upload tiếp theo")
        print("- Cập nhật database với URL đã fix nếu cần thiết")
        print("- Kiểm tra lại cấu hình S3 upload để tránh vấn đề tương tự")

if __name__ == "__main__":
    main() 