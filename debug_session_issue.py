"""
Debug script để investigate session loss issue chi tiết
"""

import asyncio
import sys
import os
import json
from datetime import datetime
from pathlib import Path

# Thêm project root vào Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.core.crawler import (
    chat_with_manus_interactive, 
    backup_session_data, 
    restore_session_data,
    check_login_status
)
from app.core.config import settings

def check_session_files(profile_name: str) -> dict:
    """
    Kiểm tra tình trạng các session files.
    """
    user_data_dir = os.path.join(settings.CHROME_PROFILE_BASE_PATH, profile_name)
    
    session_files = {
        "cookies": os.path.join(user_data_dir, "Default", "Cookies"),
        "local_storage": os.path.join(user_data_dir, "Default", "Local Storage"),
        "session_storage": os.path.join(user_data_dir, "Default", "Session Storage"),
        "indexed_db": os.path.join(user_data_dir, "Default", "IndexedDB"),
        "preferences": os.path.join(user_data_dir, "Default", "Preferences"),
        "web_data": os.path.join(user_data_dir, "Default", "Web Data")
    }
    
    status = {}
    for name, path in session_files.items():
        if os.path.exists(path):
            if os.path.isfile(path):
                status[name] = {
                    "exists": True,
                    "size": os.path.getsize(path),
                    "modified": datetime.fromtimestamp(os.path.getmtime(path)).isoformat()
                }
            else:
                # Directory
                status[name] = {
                    "exists": True,
                    "type": "directory",
                    "files_count": len(os.listdir(path)) if os.path.isdir(path) else 0,
                    "modified": datetime.fromtimestamp(os.path.getmtime(path)).isoformat()
                }
        else:
            status[name] = {"exists": False}
    
    return status

async def debug_session_preservation():
    """
    Debug session preservation step by step.
    """
    
    profile_name = "manus_login_profile"
    test_message = "Debug test message"
    test_task_url = "https://manus.im/app/670c1e4e2ce59c7c5bb9bb23"  # Thay bằng task URL thật
    
    print("🔍 DEBUG: Session Preservation Investigation")
    print("=" * 70)
    
    # Step 1: Check initial session files
    print("\n📋 Step 1: Check initial session files")
    print("-" * 40)
    initial_session = check_session_files(profile_name)
    print(json.dumps(initial_session, indent=2))
    
    # Step 2: Backup session
    print("\n📋 Step 2: Manual session backup")
    print("-" * 40)
    backup_success = await backup_session_data(profile_name)
    print(f"Backup success: {backup_success}")
    
    # Step 3: Test headless mode
    print("\n📋 Step 3: Test headless mode")
    print("-" * 40)
    
    try:
        result = await chat_with_manus_interactive(
            message=test_message,
            task_url=test_task_url,
            profile_name=profile_name,
            headless=True
        )
        
        print("✅ Headless test completed")
        print(f"Success: {result.get('success')}")
        if result.get('success'):
            session_status = result.get('session_status', {})
            print(f"Session status: {json.dumps(session_status, indent=2)}")
        else:
            print(f"Error: {result.get('error')}")
            
    except Exception as e:
        print(f"❌ Headless test failed: {e}")
    
    # Step 4: Check session files after headless
    print("\n📋 Step 4: Check session files after headless")
    print("-" * 40)
    after_headless_session = check_session_files(profile_name)
    print(json.dumps(after_headless_session, indent=2))
    
    # Step 5: Compare session changes
    print("\n📋 Step 5: Session changes analysis")
    print("-" * 40)
    
    for file_name in initial_session.keys():
        initial = initial_session.get(file_name, {})
        after = after_headless_session.get(file_name, {})
        
        if initial.get("exists") and after.get("exists"):
            if initial.get("size") != after.get("size"):
                print(f"📝 {file_name}: Size changed from {initial.get('size')} to {after.get('size')}")
            if initial.get("modified") != after.get("modified"):
                print(f"⏰ {file_name}: Modified time changed")
        elif initial.get("exists") and not after.get("exists"):
            print(f"❌ {file_name}: File disappeared!")
        elif not initial.get("exists") and after.get("exists"):
            print(f"✅ {file_name}: New file created")
    
    # Step 6: Test restore
    print("\n📋 Step 6: Test session restore")
    print("-" * 40)
    restore_success = await restore_session_data(profile_name)
    print(f"Restore success: {restore_success}")
    
    # Step 7: Final session check
    print("\n📋 Step 7: Final session files check")
    print("-" * 40)
    final_session = check_session_files(profile_name)
    print(json.dumps(final_session, indent=2))
    
    print("\n🎯 Debug completed!")

if __name__ == "__main__":
    print("🔍 Starting detailed session preservation debug...")
    print("Make sure 'manus_login_profile' is set up and logged in!")
    
    # Cho user 5 giây để cancel
    import time
    for i in range(5, 0, -1):
        print(f"Starting in {i} seconds... (Ctrl+C to cancel)")
        time.sleep(1)
    
    asyncio.run(debug_session_preservation()) 