#!/bin/bash
# Enhanced health check script for Docker container

set -e

echo "🔍 Running health check..."

# Check if FastAPI is running
if ! curl -f http://localhost:8000/health >/dev/null 2>&1; then
    echo "❌ FastAPI health check failed"
    exit 1
fi

# Check if Firebase is available (optional)
FIREBASE_CHECK=$(curl -s http://localhost:8000/debug/firebase 2>/dev/null || echo '{"firebase_available": false}')
FIREBASE_AVAILABLE=$(echo "$FIREBASE_CHECK" | grep -o '"firebase_available":[^,}]*' | cut -d':' -f2 | tr -d ' "')

if [ "$FIREBASE_AVAILABLE" = "true" ]; then
    echo "✅ Health check passed (with Firebase)"
else
    echo "✅ Health check passed (Firebase not configured)"
fi

exit 0
