###############################################################################
# Dockerfile: web_crawler_app (Playwright + FastAPI + VNC)
#
# – Cài pip install dưới root
# – Tạo start.sh dưới root, chown sang appuser
# – Trong start.sh, sử dụng $HOME (vào /home/<USER>/root
###############################################################################

FROM mcr.microsoft.com/playwright/python:v1.52.0-jammy

#########################################
# 1. Biến môi trường cơ bản
#########################################
ENV DEBIAN_FRONTEND=noninteractive \
    TZ=Asia/Ho_Chi_Minh \
    PLAYWRIGHT_BROWSERS_PATH=/ms-playwright \
    PYTHONUNBUFFERED=1 \
    CHROME_PROFILE_BASE_PATH=/app/data/chrome_profiles \
    DISPLAY=:99 \
    RESOLUTION=1920x1080x24 \
    HOME=/home/<USER>
    NOVNC_PORT=6080

WORKDIR /app

#########################################
# 2. Cài gói hệ thống và tạo thư mục
#########################################
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
      x11vnc \
      xvfb \
      openbox \
      tzdata \
      fonts-noto-color-emoji \
      fonts-noto-cjk \
      libgtk-3-0 \
      libgtk2.0-0 \
      libnss3 \
      libgbm1 \
      libxkbfile1 \
      libsecret-1-0 \
      libasound2 \
      curl \
      unzip \
      x11-utils \
      xterm \
      net-tools \
      procps \
      novnc \
      websockify \
      htop \
      psmisc \
    && ln -fs /usr/share/zoneinfo/${TZ} /etc/localtime \
    && dpkg-reconfigure --frontend noninteractive tzdata \
    && mkdir -p /app/data/chrome_profiles \
              /app/data/crawl_status \
              /app/data/firebase \
              /app/downloads \
              /home/<USER>/.vnc \
              /home/<USER>/Desktop \
    && chmod -R 775 /app/data/chrome_profiles \
                   /app/data/crawl_status \
                   /app/data/firebase \
                   /app/downloads \
                   /home/<USER>/.vnc \
                   /home/<USER>/Desktop \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# Tạo file desktop shortcut cho Chrome
RUN echo '[Desktop Entry]\nVersion=1.0\nName=Chrome\nComment=Chrome Browser\nExec=/ms-playwright/chromium-1169/chrome-linux/chrome --no-sandbox --start-maximized --user-data-dir=/app/data/chrome_profiles/default\nIcon=google-chrome\nTerminal=false\nType=Application\nCategories=Network;WebBrowser;' > /home/<USER>/Desktop/chrome.desktop

#########################################
# 3. Tạo user "appuser" với home là /home/<USER>
#########################################
RUN if ! getent group appuser; then groupadd appuser; fi && \
    if ! id -u appuser &>/dev/null; then useradd -g appuser -d /home/<USER>/bin/bash appuser; fi && \
    chown -R appuser:appuser /home/<USER>
                            /app/data/chrome_profiles \
                            /app/data/crawl_status \
                            /app/data/firebase \
                            /app/downloads \
                            /home/<USER>/Desktop/chrome.desktop

#########################################
# 4. Cập nhật pip và cài đặt các gói Python
#########################################
COPY requirements.txt /app/requirements.txt
RUN pip install --upgrade pip setuptools wheel && \
    # Cài đặt riêng google-api-python-client với phiên bản cụ thể
    pip install google-api-python-client==2.108.0 && \
    # Sửa lại requirements.txt để bỏ google-api-python-client
    grep -v "google-api-python-client" /app/requirements.txt > /app/requirements_fixed.txt && \
    # Cài đặt các gói còn lại
    pip install --no-cache-dir -r /app/requirements_fixed.txt

#########################################
# 5. Cài Chromium (Playwright) dưới root
#########################################
RUN python -m playwright install chromium

#########################################
# 6. Copy mã nguồn & run.py & alembic dưới quyền root
#########################################
COPY app/ /app/app/
COPY run.py /app/run.py
COPY alembic/ /app/alembic/
# Chuyển ownership toàn bộ source sang appuser
RUN chown -R appuser:appuser /app/app \
                            /app/run.py \
                            /app/alembic \
                            /app/data

#########################################
# 7. Tạo script start.sh
#########################################
RUN { \
    echo '#!/bin/bash'; \
    echo 'set -e'; \
    echo ''; \
    echo '# Khởi động Xvfb'; \
    echo 'Xvfb "$DISPLAY" -screen 0 "$RESOLUTION" -ac &>/home/<USER>/xvfb.log &'; \
    echo 'sleep 5'; \
    echo 'echo "✅ Xvfb started on display $DISPLAY"'; \
    echo ''; \
    echo '# Khởi động window manager (openbox)'; \
    echo 'openbox &>/dev/null &'; \
    echo 'sleep 2'; \
    echo 'echo "✅ Window manager started"'; \
    echo ''; \
    echo '# Khởi động VNC server trên port 5900 - không cần mật khẩu'; \
    echo 'x11vnc -display "$DISPLAY" -forever -shared -nopw -listen localhost -rfbport 5900 &>/home/<USER>/x11vnc.log &'; \
    echo 'sleep 2'; \
    echo 'echo "✅ VNC server started on port 5900 (no password)"'; \
    echo ''; \
    echo '# Khởi động websockify với noVNC'; \
    echo 'websockify -D --web=/usr/share/novnc ${NOVNC_PORT:-6080} localhost:5900'; \
    echo 'echo "✅ noVNC started on port ${NOVNC_PORT:-6080} - Connect to http://localhost:${NOVNC_PORT:-6080}/vnc.html"'; \
    echo ''; \
    echo '# Tạo thư mục Chrome profile mặc định nếu chưa tồn tại'; \
    echo 'mkdir -p /app/data/chrome_profiles/default'; \
    echo 'mkdir -p /app/data/crawl_status'; \
    echo 'mkdir -p /app/data/firebase'; \
    echo 'mkdir -p /app/downloads'; \
    echo ''; \
    echo '# Thêm quyền thực thi cho shortcut Chrome'; \
    echo 'chmod +x /home/<USER>/Desktop/chrome.desktop'; \
    echo ''; \
    echo '# Khởi động Chrome trong background'; \
    echo '/ms-playwright/chromium-1169/chrome-linux/chrome --no-sandbox --start-maximized --user-data-dir=/app/data/chrome_profiles/default https://google.com &'; \
    echo 'echo "✅ Chrome started"'; \
    echo ''; \
    echo '# Chạy ứng dụng Python'; \
    echo 'cd /app && python run.py || echo "⚠️ Không thể chạy ứng dụng Python: không tìm thấy run.py hoặc có lỗi!"'; \
    echo ''; \
    echo '# Để container không tắt nếu ứng dụng chính bị lỗi'; \
    echo 'echo "🔄 Container vẫn đang chạy..."'; \
    echo 'tail -f /home/<USER>/x11vnc.log'; \
  } > /app/start.sh \
  && chmod +x /app/start.sh \
  && chown appuser:appuser /app/start.sh

COPY startup.sh /app/startup.sh
COPY docker/healthcheck.sh /app/healthcheck.sh
RUN chmod +x /app/startup.sh /app/healthcheck.sh && \
    chmod -R 775 /app/data/chrome_profiles && \
    chown -R appuser:appuser /app/startup.sh /app/healthcheck.sh

#########################################
# 8. Chuyển sang user "appuser"
#########################################
USER appuser

#########################################
# 9. Expose cổng (để docker-compose map)
#########################################
EXPOSE 8000 5900 6080

#########################################
# 10. ENTRYPOINT
#########################################
CMD ["/app/start.sh"]
