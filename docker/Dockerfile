FROM mcr.microsoft.com/playwright/python:v1.52.0-jammy

ENV DEBIAN_FRONTEND=noninteractive \
    TZ=Asia/Ho_Chi_Minh \
    PLAYWRIGHT_BROWSERS_PATH=/ms-playwright \
    PYTHONUNBUFFERED=1 \
    CHROME_PROFILE_BASE_PATH=/app/data/chrome_profiles \
    DISPLAY=:99 \
    RESOLUTION=1920x1080x24 \
    HOME=/home/<USER>
    NOVNC_PORT=6080

WORKDIR /app

# Cài đặt gói hệ thống
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
      x11vnc \
      xvfb \
      openbox \
      tzdata \
      fonts-noto-color-emoji \
      fonts-noto-cjk \
      libgtk-3-0 \
      libgtk2.0-0 \
      libnss3 \
      libgbm1 \
      libxkbfile1 \
      libsecret-1-0 \
      libasound2 \
      curl \
      unzip \
      x11-utils \
      xterm \
      net-tools \
      procps \
      novnc \
      websockify \
      htop \
      psmisc \
    && ln -fs /usr/share/zoneinfo/${TZ} /etc/localtime && \
    dpkg-reconfigure --frontend noninteractive tzdata && \
    mkdir -p /app/data/chrome_profiles/default \
             /app/data/crawl_status \
             /home/<USER>/.vnc \
             /home/<USER>/Desktop && \
    chmod -R 775 /app/data /home/<USER>
    apt-get clean && rm -rf /var/lib/apt/lists/*

# Desktop shortcut cho Chrome
RUN echo '[Desktop Entry]\nVersion=1.0\nName=Chrome\nComment=Chrome Browser\nExec=/ms-playwright/chromium-1169/chrome-linux/chrome --no-sandbox --start-maximized --user-data-dir=/app/data/chrome_profiles/default\nIcon=google-chrome\nTerminal=false\nType=Application\nCategories=Network;WebBrowser;' > /home/<USER>/Desktop/chrome.desktop

# Tạo user appuser
RUN groupadd -f appuser || true && \
    usermod -d /home/<USER>/bin/bash appuser || true && \
    mkdir -p /home/<USER>/app/data/chrome_profiles/default /home/<USER>/Desktop && \
    chown -R appuser:appuser /home/<USER>/app/data /home/<USER>/Desktop


# Cài đặt pip & các package
COPY requirements.txt /app/requirements.txt
RUN pip install --upgrade pip setuptools wheel && \
    pip install google-api-python-client==2.108.0 && \
    grep -v "google-api-python-client" /app/requirements.txt > /app/requirements_fixed.txt && \
    pip install --no-cache-dir -r /app/requirements_fixed.txt

# Cài Chromium
RUN python -m playwright install chromium

# Copy source code
COPY app/ /app/app/
COPY run.py /app/run.py
RUN chown -R appuser:appuser /app/app /app/run.py /app/data

# Tạo start.sh
RUN { \
    echo '#!/bin/bash'; \
    echo 'set -e'; \
    echo 'Xvfb "$DISPLAY" -screen 0 "$RESOLUTION" -ac &>/home/<USER>/xvfb.log &'; \
    echo 'sleep 5'; \
    echo 'echo "✅ Xvfb started on display $DISPLAY"'; \
    echo 'openbox &>/dev/null &'; \
    echo 'sleep 2'; \
    echo 'echo "✅ Window manager started"'; \
    echo 'x11vnc -display "$DISPLAY" -forever -shared -nopw -listen localhost -rfbport 5900 &>/home/<USER>/x11vnc.log &'; \
    echo 'sleep 2'; \
    echo 'echo "✅ VNC server started on port 5900 (no password)"'; \
    echo 'websockify -D --web=/usr/share/novnc ${NOVNC_PORT:-6080} localhost:5900'; \
    echo 'echo "✅ noVNC started on port ${NOVNC_PORT:-6080} - Connect to http://localhost:${NOVNC_PORT:-6080}/vnc.html"'; \
    echo 'mkdir -p /app/data/chrome_profiles/default'; \
    echo 'chmod +x /home/<USER>/Desktop/chrome.desktop'; \
    echo '/ms-playwright/chromium-1169/chrome-linux/chrome --no-sandbox --start-maximized --user-data-dir=/app/data/chrome_profiles/default https://google.com &'; \
    echo 'echo "✅ Chrome started"'; \
    echo 'cd /app && python run.py || echo "⚠️ Không thể chạy ứng dụng Python!"'; \
    echo 'echo "🔄 Container vẫn đang chạy..."'; \
    echo 'tail -f /home/<USER>/x11vnc.log'; \
} > /app/start.sh && \
chmod +x /app/start.sh && \
chown appuser:appuser /app/start.sh

# Copy startup.sh nếu bạn có
COPY startup.sh /app/startup.sh
RUN chmod +x /app/startup.sh && \
    chown appuser:appuser /app/startup.sh

# Đảm bảo quyền cho toàn bộ thư mục data
RUN chmod -R 775 /app/data && \
    chown -R appuser:appuser /app/data

USER appuser

EXPOSE 8000 5900 6080

CMD ["/app/start.sh"]
