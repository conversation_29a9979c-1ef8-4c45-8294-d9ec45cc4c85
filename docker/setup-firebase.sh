#!/bin/bash
# <PERSON><PERSON><PERSON> để setup Firebase credentials cho Docker

set -e

echo "🔥 Setting up Firebase for Docker..."

# T<PERSON><PERSON> thư mục data nếu chưa có
mkdir -p ./data

# Kiểm tra .env file
if [ ! -f .env ]; then
    echo "📝 Creating .env file from .env.example..."
    cp .env.example .env
    echo "⚠️ Please edit .env file with your Firebase settings!"
else
    echo "✅ .env file already exists"
fi

# Kiểm tra Firebase credentials
if [ ! -f ./data/firebase-credentials.json ]; then
    echo "⚠️ Firebase credentials not found!"
    echo "📋 Please follow these steps:"
    echo "1. Go to Firebase Console: https://console.firebase.google.com/"
    echo "2. Select your project"
    echo "3. Go to Project Settings > Service accounts"
    echo "4. Click 'Generate new private key'"
    echo "5. Save the downloaded JSON file as './data/firebase-credentials.json'"
    echo ""
    echo "📁 Expected file location: ./data/firebase-credentials.json"
    
    # Tạo file placeholder
    cat > ./data/firebase-credentials.json << 'EOF'
*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
EOF
    echo "📄 Created placeholder firebase-credentials.json"
    echo "🔧 Please replace with your actual Firebase credentials!"
else
    echo "✅ Firebase credentials file found"
fi

# Kiểm tra Firebase Database URL trong .env
if ! grep -q "FIREBASE_DATABASE_URL=https://" .env; then
    echo "⚠️ Please set FIREBASE_DATABASE_URL in .env file"
    echo "📋 Format: FIREBASE_DATABASE_URL=https://your-project-id-default-rtdb.firebaseio.com/"
fi

# Tạo thư mục downloads nếu chưa có
mkdir -p ./downloads

echo ""
echo "✅ Firebase setup completed!"
echo ""
echo "📋 Next steps:"
echo "1. Edit .env file with your Firebase settings"
echo "2. Replace ./data/firebase-credentials.json with your actual credentials"
echo "3. Run: docker-compose up --build"
echo ""
echo "🔍 To test Firebase connection:"
echo "curl http://localhost:8000/debug/firebase"
