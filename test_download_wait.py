#!/usr/bin/env python3
"""
Test script để kiểm tra logic đợi download hoàn thành trong crawl_manus_page_content
"""

import requests
import json
import time

def test_crawl_with_download_wait():
    """Test crawl API với logic đợi download mới"""
    
    url = "http://localhost:8000/crawl-url/"
    
    # Test với task có files để download
    payload = {
        "url": "https://manus.im/app/ymtWSHPNtH67PkCQovsFJJ",  # Task có files
        "profile_name": "manus_login_profile",
        "headless": True
    }
    
    print("🧪 Testing crawl with enhanced download wait logic...")
    print(f"📤 Payload: {json.dumps(payload, indent=2)}")
    
    try:
        start_time = time.time()
        response = requests.post(url, json=payload, timeout=300)  # 5 phút timeout
        end_time = time.time()
        
        execution_time = end_time - start_time
        
        print(f"📥 Status Code: {response.status_code}")
        print(f"⏱️ Execution Time: {execution_time:.2f} seconds")
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get("success"):
                crawl_data = data.get("data", {})
                downloaded_files = crawl_data.get("downloaded_files", [])
                total_files = data.get("total_files", 0)
                
                print(f"✅ Crawl successful!")
                print(f"📄 Page Title: {crawl_data.get('page_title', 'N/A')}")
                print(f"📝 Task Title: {crawl_data.get('current_task_title', 'N/A')}")
                print(f"💬 Chat Messages: {len(crawl_data.get('chat_messages', []))}")
                print(f"📥 Downloaded Files: {len(downloaded_files)}")
                print(f"📊 Total Files: {total_files}")
                
                # Hiển thị chi tiết downloaded files
                print("\n📁 Downloaded Files Details:")
                for i, file_info in enumerate(downloaded_files):
                    file_type = file_info.get("type", "file")
                    if file_type == "zip_summary":
                        print(f"  {i+1}. [ZIP SUMMARY] {file_info.get('zip_filename')}")
                        print(f"      Total extracted: {file_info.get('total_extracted')}")
                        print(f"      Local path: {file_info.get('local_path')}")
                    else:
                        print(f"  {i+1}. [FILE] {file_info.get('filename')}")
                        print(f"      Size: {file_info.get('file_size')} bytes")
                        print(f"      Type: {file_info.get('content_type')}")
                        print(f"      S3 URL: {file_info.get('s3_url')}")
                        if file_info.get('extracted_from_zip'):
                            print(f"      Extracted from: {file_info.get('extracted_from_zip')}")
                
                # Kiểm tra cloudinary_files trong chat messages
                print("\n💬 Chat Messages with Files:")
                for i, msg in enumerate(crawl_data.get('chat_messages', [])):
                    if msg.get('cloudinary_files'):
                        print(f"  Message {i+1}: {len(msg['cloudinary_files'])} files")
                        msg_type = msg.get('type', 'unknown')
                        if msg_type == 'manus':
                            preview = msg.get('manus_message', '')[:50]
                            print(f"    Manus: {preview}...")
                
                return True
            else:
                print(f"❌ Crawl failed: {data.get('error', 'Unknown error')}")
                return False
        else:
            print(f"❌ HTTP error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing crawl: {str(e)}")
        return False

def test_scheduler_with_download_wait():
    """Test scheduler với logic download mới"""
    
    print("\n🔄 Testing scheduler with download wait logic...")
    
    # Gọi chat API để tạo task mới
    chat_url = "http://localhost:8000/chat-with-manus-realtime/"
    
    timestamp = str(int(time.time()))
    chat_payload = {
        "message": "Test download wait logic",
        "task_url": f"https://manus.im/app/test-download-{timestamp}",
        "profile_name": "manus_login_profile",
        "request_id": f"test-download-{timestamp}",
        "headless": True,
        "user_id": f"download_test_user_{timestamp}"
    }
    
    try:
        print("📤 Creating new task via chat API...")
        response = requests.post(chat_url, json=chat_payload, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            task_id = data.get("task_id")
            user_id = data.get("user_id")
            
            print(f"✅ Task created: {task_id}")
            print(f"👤 User ID: {user_id}")
            
            # Kiểm tra scheduler status
            time.sleep(3)  # Đợi scheduler khởi động
            
            scheduler_url = "http://localhost:8000/debug/scheduler"
            scheduler_response = requests.get(scheduler_url, timeout=10)
            
            if scheduler_response.status_code == 200:
                scheduler_data = scheduler_response.json()
                scheduler_status = scheduler_data.get("scheduler_status", {})
                total_tasks = scheduler_status.get("total_tasks", 0)
                
                print(f"📊 Scheduler monitoring {total_tasks} task(s)")
                
                if total_tasks > 0:
                    tasks = scheduler_status.get("tasks", {})
                    for tid, task_info in tasks.items():
                        print(f"  📋 Task {tid}: {task_info.get('status', 'unknown')}")
                
                return True
            else:
                print("❌ Failed to check scheduler status")
                return False
        else:
            print(f"❌ Failed to create task: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing scheduler: {str(e)}")
        return False

def show_download_improvements():
    """Hiển thị các cải tiến trong logic download"""
    
    print("\n📋 Download Logic Improvements:")
    print("=" * 50)
    
    print("🔧 Before (Old Logic):")
    print("  - Fixed 5-second wait after batch download")
    print("  - No tracking of download completion")
    print("  - May return before uploads finish")
    print("  - No progress feedback")
    
    print("\n✅ After (New Logic):")
    print("  - Count expected downloads before starting")
    print("  - Track download completion count")
    print("  - Wait until all downloads complete")
    print("  - 2-minute timeout with progress updates")
    print("  - Enhanced progress messages")
    print("  - Better error handling")
    
    print("\n🔄 New Flow:")
    print("  1. Count files to download")
    print("  2. Set up download tracking")
    print("  3. Click batch download button")
    print("  4. Wait for all downloads to complete")
    print("  5. Process and upload each file")
    print("  6. Update Firebase with results")
    
    print("\n📊 Progress Messages:")
    print("  - 📥 Starting download: filename")
    print("  - 📦 Giải nén ZIP: filename -> X files")
    print("  - ✅ File uploaded: filename -> S3 URL")
    print("  - 📊 Download progress: X/Y completed")
    print("  - ✅ All downloads completed: X/Y")

if __name__ == "__main__":
    print("🧪 Testing Enhanced Download Wait Logic\n")
    
    # Show improvements
    show_download_improvements()
    
    # Test direct crawl
    print("\n" + "="*50)
    crawl_success = test_crawl_with_download_wait()
    
    if crawl_success:
        print("\n✅ Direct crawl test passed!")
    else:
        print("\n❌ Direct crawl test failed!")
    
    # Test scheduler
    print("\n" + "="*50)
    scheduler_success = test_scheduler_with_download_wait()
    
    if scheduler_success:
        print("\n✅ Scheduler test passed!")
    else:
        print("\n❌ Scheduler test failed!")
    
    print("\n📋 Summary:")
    print(f"✅ Direct Crawl: {'PASS' if crawl_success else 'FAIL'}")
    print(f"✅ Scheduler: {'PASS' if scheduler_success else 'FAIL'}")
    
    if crawl_success and scheduler_success:
        print("\n🎉 All tests passed! Download wait logic is working correctly.")
    else:
        print("\n⚠️ Some tests failed. Check the logs for details.")
    
    print("\n🎯 Next steps:")
    print("1. Monitor server logs for download progress messages")
    print("2. Check Firebase for complete downloaded_files data")
    print("3. Verify all files are uploaded to S3")
    print("4. Test with different file types and sizes")
