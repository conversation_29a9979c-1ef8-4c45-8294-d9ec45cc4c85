#!/usr/bin/env python3
"""
Script đơn giản để khắc phục URL S3 bị lỗi do encoding ký tự tiếng Việt và region endpoint
"""

import re
from urllib.parse import unquote
try:
    from unidecode import unidecode
except ImportError:
    print("⚠️ Module unidecode chưa được cài đặt. Sử dụng fallback method.")
    def unidecode(text):
        # Fallback: chỉ giữ lại ASCII characters
        return ''.join(char if ord(char) < 128 else '_' for char in text)

def sanitize_path_component(path_component: str) -> str:
    """
    Sanitize một thành phần path để phù hợp với S3 key requirements
    """
    try:
        # Bước 1: Chuyển đổi unicode thành ASCII
        ascii_component = unidecode(path_component)
        
        # Bước 2: Thay thế các ký tự không phù hợp
        invalid_chars = ['\\', '{', '^', '}', '%', '`', ']', '"', '>', '[', '~', '<', '#', '|']
        sanitized = ascii_component
        for char in invalid_chars:
            sanitized = sanitized.replace(char, '_')
        
        # Bước 3: Thay thế khoảng trắng bằng dấu gạch dưới
        sanitized = sanitized.replace(' ', '_')
        
        # Bước 4: Loại bỏ dấu gạch dưới liên tiếp
        sanitized = re.sub(r'_+', '_', sanitized)
        
        # Bước 5: Loại bỏ dấu gạch dưới ở đầu và cuối
        sanitized = sanitized.strip('_')
        
        # Bước 6: Đảm bảo không rỗng
        if not sanitized:
            sanitized = 'unnamed'
            
        return sanitized
        
    except Exception as e:
        print(f"⚠️ Lỗi khi sanitize: {str(e)}")
        return ''.join(char if char.isalnum() else '_' for char in path_component).strip('_') or 'unnamed'

def analyze_broken_url(broken_url: str):
    """
    Phân tích URL bị lỗi
    """
    print("🔍 PHÂN TÍCH URL BỊ LỖI")
    print("=" * 50)
    print(f"URL gốc: {broken_url}")
    
    # Parse URL - hỗ trợ cả format cũ và mới
    url_patterns = [
        r'https://([^/]+)\.s3\.([^/]+)\.amazonaws\.com/(.+)',  # format: bucket.s3.region.amazonaws.com
        r'https://([^/]+)\.s3-([^/]+)\.amazonaws\.com/(.+)',   # format: bucket.s3-region.amazonaws.com
    ]
    
    match = None
    for pattern in url_patterns:
        match = re.match(pattern, broken_url)
        if match:
            break
    
    if match:
        bucket_name = match.group(1)
        region = match.group(2)
        s3_path = match.group(3)
        
        print(f"📦 Bucket: {bucket_name}")
        print(f"🌍 Region: {region}")
        print(f"🔗 S3 Path (encoded): {s3_path}")
        
        # Decode path
        decoded_path = unquote(s3_path)
        print(f"📄 S3 Path (decoded): {decoded_path}")
        
        # Phân tích các thành phần
        path_parts = decoded_path.split('/')
        print(f"📁 Path components:")
        for i, part in enumerate(path_parts):
            print(f"   {i+1}. '{part}'")
            
        return {
            'bucket': bucket_name,
            'region': region,
            'path_parts': path_parts,
            'decoded_path': decoded_path
        }
    else:
        print("❌ URL không đúng định dạng S3")
        return None

def fix_url(broken_url: str, correct_region: str = None):
    """
    Khắc phục URL bị lỗi (cả encoding và region)
    """
    print("\n🔧 KHẮC PHỤC URL")
    print("=" * 50)
    
    # Parse URL - hỗ trợ cả format cũ và mới
    url_patterns = [
        r'https://([^/]+)\.s3\.([^/]+)\.amazonaws\.com/(.+)',  # format: bucket.s3.region.amazonaws.com
        r'https://([^/]+)\.s3-([^/]+)\.amazonaws\.com/(.+)',   # format: bucket.s3-region.amazonaws.com
    ]
    
    match = None
    for pattern in url_patterns:
        match = re.match(pattern, broken_url)
        if match:
            break
    
    if not match:
        print("❌ URL không đúng định dạng S3")
        return broken_url
    
    bucket_name = match.group(1)
    current_region = match.group(2)
    s3_path = match.group(3)
    
    # Sử dụng region đúng nếu được cung cấp
    final_region = correct_region if correct_region else current_region
    
    print(f"📦 Bucket: {bucket_name}")
    print(f"🌍 Region hiện tại: {current_region}")
    if correct_region:
        print(f"🌍 Region đúng: {final_region}")
    
    # Decode path
    decoded_path = unquote(s3_path)
    path_parts = decoded_path.split('/')
    
    # Sanitize từng phần
    sanitized_parts = []
    for part in path_parts:
        if part:  # Bỏ qua empty parts
            sanitized_part = sanitize_path_component(part)
            sanitized_parts.append(sanitized_part)
            if part != sanitized_part:
                print(f"   '{part}' -> '{sanitized_part}'")
    
    # Tạo S3 key mới
    new_s3_key = '/'.join(sanitized_parts)
    
    # Tạo URL mới với region đúng
    fixed_url = f"https://{bucket_name}.s3-{final_region}.amazonaws.com/{new_s3_key}"
    
    print(f"\n🔑 S3 Key cũ: {s3_path}")
    print(f"🔑 S3 Key mới: {new_s3_key}")
    print(f"✅ URL đã sửa: {fixed_url}")
    
    return fixed_url

def main():
    """
    Main function
    """
    print("🚀 SCRIPT KHẮC PHỤC URL S3 BỊ LỖI (ENCODING + REGION)")
    print("=" * 60)
    
    # Danh sách URL bị lỗi
    broken_urls = [
        {
            "url": "https://storage-file-s3-v1.s3.us-east-1.amazonaws.com/crawl_results/4vwIS7665INBoQ2Y1UMOWt/T%E1%BB%95ng%20h%E1%BB%A3p%20gi%C3%A1%20v%C3%A0ng%20h%C3%B4m%20nay%20pdf/gold_price_report.pdf",
            "correct_region": "ap-southeast-1",
            "issue": "Encoding + Wrong Region"
        },
        {
            "url": "https://storage-file-s3-v1.s3.us-east-1.amazonaws.com/crawl_results/vKLo2XHQfIr1ejBLCMqgFb/Tong_hop_gia_vang_hom_nay_file_PDF/gold_price_report.pdf",
            "correct_region": "ap-southeast-1", 
            "issue": "Wrong Region"
        }
    ]
    
    for i, item in enumerate(broken_urls, 1):
        print(f"\n{'='*60}")
        print(f"🔧 CASE {i}: {item['issue']}")
        print(f"{'='*60}")
        
        broken_url = item["url"]
        correct_region = item.get("correct_region")
        
        # Phân tích URL
        analysis = analyze_broken_url(broken_url)
        
        if analysis:
            # Khắc phục URL
            fixed_url = fix_url(broken_url, correct_region)
            
            # Thông tin trích xuất
            if len(analysis['path_parts']) >= 3:
                task_id = analysis['path_parts'][1]
                subfolder = analysis['path_parts'][2]
                filename = analysis['path_parts'][3] if len(analysis['path_parts']) > 3 else "unknown_file"
                
                print(f"\n📋 THÔNG TIN TRÍCH XUẤT:")
                print(f"   Task ID: {task_id}")
                print(f"   Subfolder gốc: {subfolder}")
                print(f"   Subfolder sanitized: {sanitize_path_component(subfolder)}")
                print(f"   Filename gốc: {filename}")
                print(f"   Filename sanitized: {sanitize_path_component(filename)}")
            
            print(f"\n📝 KẾT QUẢ:")
            print(f"🔴 URL gốc (bị lỗi):")
            print(f"   {broken_url}")
            print(f"🟢 URL đã fix:")
            print(f"   {fixed_url}")
    
    print(f"\n{'='*60}")
    print("💡 KHUYẾN NGHỊ:")
    print("1. Sử dụng URL đã fix (màu xanh) để truy cập file")
    print("2. Cập nhật AWS_REGION trong config thành 'ap-southeast-1'")
    print("3. Kiểm tra tất cả các URL S3 khác có thể bị lỗi tương tự")
    print("4. Cập nhật database với URL đã fix")
    print("5. Từ nay sử dụng region ap-southeast-1 cho tất cả S3 operations")

if __name__ == "__main__":
    main() 