"""
Test script để test tấ<PERSON> cả approaches để fix session issue
"""

import asyncio
import sys
import os

# Thêm project root vào Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.core.crawler import (
    chat_with_manus_interactive,
    extract_cookies_from_profile,
    backup_session_data,
    restore_session_data
)

async def test_multiple_approaches():
    """
    Test multiple approaches để fix session loss issue.
    """
    
    # Cấu h<PERSON> test
    test_message = "Hello, this is a test message to verify session preservation"
    test_task_url = "https://manus.im/app/670c1e4e2ce59c7c5bb9bb23"  # Thay bằng task URL thật
    test_profile = "manus_login_profile"
    
    print("🧪 Testing Multiple Approaches for Session Preservation")
    print("=" * 70)
    
    # Approach 1: Original approach với session preservation
    print("\n📋 Approach 1: Session Preservation Mode")
    print("-" * 50)
    
    try:
        result_1 = await chat_with_manus_interactive(
            message=test_message,
            task_url=test_task_url,
            profile_name=test_profile,
            headless=True
        )
        
        print("✅ Approach 1 completed")
        session_status_1 = result_1.get("session_status", {})
        print(f"   - Success: {result_1.get('success')}")
        print(f"   - Still logged in: {session_status_1.get('still_logged_in_after_chat')}")
        print(f"   - Stealth mode used: {session_status_1.get('stealth_mode_used')}")
        print(f"   - Session backup restored: {session_status_1.get('session_backup_restored')}")
        
        if not result_1.get("success"):
            print(f"   - Error: {result_1.get('error')}")
            
    except Exception as e:
        print(f"❌ Approach 1 failed: {e}")
    
    # Đợi giữa các test
    print("\n⏳ Waiting 10 seconds before next test...")
    await asyncio.sleep(10)
    
    # Approach 2: Extract cookies trước để check
    print("\n📋 Approach 2: Pre-extract Cookies")
    print("-" * 50)
    
    try:
        print("Extracting cookies before test...")
        cookies = await extract_cookies_from_profile(test_profile)
        print(f"Extracted {len(cookies)} cookies")
        
        result_2 = await chat_with_manus_interactive(
            message=test_message + " (with pre-extracted cookies)",
            task_url=test_task_url,
            profile_name=test_profile,
            headless=True
        )
        
        print("✅ Approach 2 completed")
        session_status_2 = result_2.get("session_status", {})
        print(f"   - Success: {result_2.get('success')}")
        print(f"   - Still logged in: {session_status_2.get('still_logged_in_after_chat')}")
        
        if not result_2.get("success"):
            print(f"   - Error: {result_2.get('error')}")
            
    except Exception as e:
        print(f"❌ Approach 2 failed: {e}")
    
    # Đợi giữa các test
    print("\n⏳ Waiting 10 seconds before next test...")
    await asyncio.sleep(10)
    
    # Approach 3: Non-headless baseline
    print("\n📋 Approach 3: Non-headless Baseline")
    print("-" * 50)
    
    try:
        result_3 = await chat_with_manus_interactive(
            message=test_message + " (non-headless baseline)",
            task_url=test_task_url,
            profile_name=test_profile,
            headless=False
        )
        
        print("✅ Approach 3 completed")
        session_status_3 = result_3.get("session_status", {})
        print(f"   - Success: {result_3.get('success')}")
        print(f"   - Still logged in: {session_status_3.get('still_logged_in_after_chat')}")
        
        if not result_3.get("success"):
            print(f"   - Error: {result_3.get('error')}")
            
    except Exception as e:
        print(f"❌ Approach 3 failed: {e}")
    
    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 70)
    
    approaches = [
        ("Stealth Headless + Session Backup", 'result_1'),
        ("Cookie Injection + Headless", 'result_2'), 
        ("Non-headless Baseline", 'result_3')
    ]
    
    for name, var_name in approaches:
        if var_name in locals():
            result = locals()[var_name]
            success = result.get('success', False)
            session_status = result.get('session_status', {})
            still_logged_in = session_status.get('still_logged_in_after_chat', False)
            
            status_icon = "✅" if success and still_logged_in else "❌" if success else "💥"
            print(f"{status_icon} {name}: Success={success}, Session Preserved={still_logged_in}")
        else:
            print(f"💥 {name}: Failed to run")
    
    print("\n🎯 Testing completed!")
    
    # Recommendations
    print("\n💡 Recommendations:")
    if 'result_1' in locals() and result_1.get('success') and result_1.get('session_status', {}).get('still_logged_in_after_chat'):
        print("✅ Use Stealth Headless + Session Backup approach")
    elif 'result_2' in locals() and result_2.get('success') and result_2.get('session_status', {}).get('still_logged_in_after_chat'):
        print("✅ Use Cookie Injection + Headless approach")
    else:
        print("⚠️  Consider using non-headless mode as fallback")
        print("⚠️  Or investigate Manus.im anti-automation measures")

if __name__ == "__main__":
    print("🧪 Starting comprehensive session preservation test...")
    print("Make sure 'manus_login_profile' is set up and logged in!")
    print("This test will take several minutes to complete...")
    
    # Cho user 10 giây để cancel
    import time
    for i in range(10, 0, -1):
        print(f"Starting in {i} seconds... (Ctrl+C to cancel)")
        time.sleep(1)
    
    asyncio.run(test_multiple_approaches()) 