#!/usr/bin/env python3
"""
Simple upload server để bypass vấn đ<PERSON> FastAPI multipart parsing
"""

from flask import Flask, request, jsonify
import asyncio
import os
from app.utils.s3_upload import s3_uploader
from app.utils.crawl_status_manager import crawl_status_manager

app = Flask(__name__)

@app.route('/simple-upload', methods=['POST'])
def simple_upload():
    """
    Simple upload endpoint sử dụng Flask
    """
    try:
        # Lấy file từ request
        if 'file' not in request.files:
            return jsonify({
                "success": False,
                "error": "No file provided"
            }), 400
        
        file = request.files['file']
        task_id = request.form.get('task_id')
        subfolder = request.form.get('subfolder')
        
        if not task_id:
            return jsonify({
                "success": False,
                "error": "task_id is required"
            }), 400
        
        if file.filename == '':
            return jsonify({
                "success": False,
                "error": "No file selected"
            }), 400
        
        # <PERSON><PERSON><PERSON> file content
        file_content = file.read()
        
        # Upload sử dụng S3Uploader
        async def upload_async():
            upload_result = await s3_uploader.upload_file_bytes(
                file_bytes=file_content,
                filename=file.filename,
                task_id=task_id,
                subfolder=subfolder,
                content_type=file.content_type
            )
            
            if upload_result:
                # Tạo task nếu chưa có
                task_status = await crawl_status_manager.get_task_status(task_id)
                if not task_status:
                    await crawl_status_manager.create_task(task_id, {"source": "simple_upload"})
                
                # Cập nhật task status với file đã upload
                await crawl_status_manager.add_uploaded_file(task_id, upload_result)
            
            return upload_result
        
        # Chạy async function
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        upload_result = loop.run_until_complete(upload_async())
        loop.close()
        
        if upload_result:
            return jsonify({
                "success": True,
                "filename": upload_result["filename"],
                "s3_url": upload_result["s3_url"],
                "s3_bucket": upload_result["s3_bucket"],
                "s3_key": upload_result["s3_key"],
                "file_size": upload_result["file_size"],
                "content_type": upload_result["content_type"],
                "upload_time": upload_result["upload_time"],
                "message": "File uploaded successfully via simple server"
            })
        else:
            return jsonify({
                "success": False,
                "error": "Failed to upload file to S3"
            }), 500
            
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/health', methods=['GET'])
def health():
    """Health check endpoint"""
    return jsonify({
        "status": "healthy",
        "message": "Simple upload server is running"
    })

if __name__ == '__main__':
    print("🚀 Starting simple upload server on port 8001...")
    print("✅ S3 bucket:", s3_uploader.bucket_name)
    app.run(host='0.0.0.0', port=8001, debug=True) 