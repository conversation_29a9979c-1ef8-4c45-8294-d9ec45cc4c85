# 📁 Cloudinary Integration API Guide

Hướng dẫn sử dụng các API mới đã được cập nhật để upload file lên **Cloudinary** thay vì Google Drive.

## 🔄 Thay Đổi Chính

### Cũ: Google Drive

- File được upload lên Google Drive
- Sử dụng Service Account JSON
<PERSON> <PERSON><PERSON><PERSON> về Google Drive file ID

### Mới: Cloudinary

- File được upload lên Cloudinary
- Sử dụng API Key và Upload Preset
- Trả về Cloudinary secure URL

## 🚀 API Endpoints Mới

### 1. Check Crawl Status (Đã cập nhật)

```http
POST /check-crawl-status/
```

**Request:**

```json
{
  "task_id": "your-task-id",
  "profile_name": "optional-profile-name",
  "headless": true
}
```

**Response:**

```json
{
  "success": true,
  "task_id": "your-task-id",
  "status": "completed",
  "message": "Task your-task-id is completed",
  "last_updated": "2024-01-20T10:30:00",
  "uploaded_files": [
    {
      "filename": "document.pdf",
      "cloudinary_url": "https://res.cloudinary.com/test-upload-1/image/upload/v1/crawl_results/task-id/document.pdf",
      "public_id": "crawl_results/task-id/document",
      "resource_type": "auto",
      "format": "pdf",
      "bytes": 1024000,
      "upload_time": "2024-01-20T10:25:00",
      "uploaded_at": "2024-01-20T10:25:05"
    }
  ]
}
```

### 2. Get Uploaded Files (Mới)

```http
POST /get-uploaded-files/
```

**Request:**

```json
{
  "task_id": "your-task-id"
}
```

**Response:**

```json
{
  "success": true,
  "task_id": "your-task-id",
  "files": [
    {
      "filename": "document.pdf",
      "cloudinary_url": "https://res.cloudinary.com/test-upload-1/...",
      "public_id": "crawl_results/task-id/document",
      "resource_type": "auto",
      "format": "pdf",
      "bytes": 1024000,
      "upload_time": "2024-01-20T10:25:00",
      "uploaded_at": "2024-01-20T10:25:05",
      "local_path": "downloads/task-id/document.pdf"
    }
  ],
  "total_files": 1,
  "task_status": "completed"
}
```

### 3. Admin Cleanup Old Tasks (Mới)

```http
POST /admin/cleanup-old-tasks/
Headers: X-API-KEY: your-admin-key
```

**Request Parameters:**

- `days` (query param): Số ngày (default: 7)

**Response:**

```json
{
  "success": true,
  "message": "Đã xóa 5 task cũ hơn 7 ngày",
  "deleted_count": 5,
  "days": 7
}
```

## 📊 Task Status Flow

```
PENDING → IN_PROGRESS → UPLOADING → COMPLETED
    ↓           ↓           ↓
  ERROR ←──── ERROR ←──── ERROR
```

### Status Descriptions:

- **PENDING**: Task vừa được tạo
- **IN_PROGRESS**: Đang crawl dữ liệu
- **UPLOADING**: Đang upload file lên Cloudinary
- **COMPLETED**: Hoàn thành crawl và upload
- **ERROR**: Có lỗi xảy ra

## 🔧 Cấu Hình Cloudinary

### Environment Variables (nếu cần):

```env
CLOUDINARY_CLOUD_NAME=test-upload-1
CLOUDINARY_API_KEY=238276161954221
CLOUDINARY_UPLOAD_PRESET=AutoWork
```

### File Organization:

Files được tổ chức theo structure:

```
cloudinary://
└── crawl_results/
    ├── task-id-1/
    │   ├── file1.pdf
    │   └── file2.docx
    └── task-id-2/
        └── file3.xlsx
```

## 💡 Sử Dụng Thực Tế

### Crawl và Track Files:

```javascript
// 1. Bắt đầu crawl
const crawlResponse = await fetch("/crawl-url/", {
  method: "POST",
  headers: { "Content-Type": "application/json" },
  body: JSON.stringify({
    url: "https://manus.im/task/your-task-id",
    profile_name: "manus_login_profile",
    headless: true,
  }),
});

const { task_id } = await crawlResponse.json();

// 2. Polling status
const checkStatus = async () => {
  const response = await fetch("/check-crawl-status/", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ task_id }),
  });

  const result = await response.json();

  if (result.status === "completed") {
    console.log("✅ Files uploaded:", result.uploaded_files);
    return result.uploaded_files;
  } else if (result.status === "error") {
    console.error("❌ Error:", result.error);
    return null;
  }

  // Tiếp tục polling
  setTimeout(checkStatus, 5000);
};

checkStatus();
```

### Get File List:

```javascript
const getFiles = async (taskId) => {
  const response = await fetch("/get-uploaded-files/", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ task_id: taskId }),
  });

  const result = await response.json();
  return result.files;
};
```

## 🔒 Bảo Mật

### File Access:

- Cloudinary URLs là public accessible
- Có thể cấu hình private delivery nếu cần
- Files được tổ chức theo task_id để tránh conflict

### Admin APIs:

- Cleanup API yêu cầu X-API-KEY header
- Chỉ admin mới có thể cleanup old tasks

## 🐛 Error Handling

### Common Errors:

```json
{
  "success": false,
  "task_id": "task-id",
  "status": "error",
  "error": "Task not found"
}
```

### Upload Errors:

- Nếu upload Cloudinary thất bại, file vẫn được lưu local
- Field `upload_error` sẽ chứa thông tin lỗi
- `cloudinary_url` sẽ là `null`

## 📈 Performance

### File Size Limits:

- Cloudinary free tier: 10MB per file
- Có thể upgrade plan nếu cần files lớn hơn

### Storage:

- Local files tự động cleanup sau upload thành công
- Cloudinary files persist theo cấu hình của account

## 🔧 Troubleshooting

### Common Issues:

1. **Upload Cloudinary thất bại:**

   - Check API key và upload preset
   - Verify file size < limit
   - Check network connectivity

2. **Task không tìm thấy:**

   - Verify task_id format
   - Check task đã được tạo chưa

3. **Profile errors:**
   - Ensure Chrome profile exists
   - Check profile permissions

### Debug Tips:

- Check server logs cho upload errors
- Verify Cloudinary dashboard cho uploaded files
- Use `/admin/list-profiles/` để check profiles
