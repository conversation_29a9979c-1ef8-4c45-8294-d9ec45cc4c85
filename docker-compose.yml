services:
  ############################################################
  # web_crawler_app: FastAPI + <PERSON>wright + VNC + noVNC
  ############################################################
  web_crawler_app:
    build:
      context: .
      dockerfile: docker/Dockerfile
    container_name: manus_crawler_service

    # Bind-mount mã nguồn và Chrome profiles
    volumes:
      - ./app:/app/app                          
      - ./data:/app/data   # <== mount toàn bộ data
      - ./downloads:/app/downloads
      # Tạo mới file run.py nếu chưa tồn tại
      - ./run.py:/app/run.py

    # Mở port chỉ trên localhost (DEV môi trường); nếu cần public, đ<PERSON><PERSON> thành 0.0.0.0
    ports:
      - "0.0.0.0:8000:8000"                   
      - "0.0.0.0:5900:5900"                   
      - "0.0.0.0:6080:6080"                   # noVNC web interface

    # Quản lý secret và biến môi trường
    env_file:
      - .env
    environment:
      - PYTHONUNBUFFERED=1
      - CHROME_PROFILE_BASE_PATH=/app/data/chrome_profiles
      - DISPLAY=:99
      - VNC_PASSWORD=admin123
      - NOVNC_PORT=6080
    command: /app/start.sh

    # Healthcheck để đảm bảo service thực sự ready
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8000/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s

    networks:
      - manus_network

    restart: unless-stopped

networks:
  manus_network:
    driver: bridge
