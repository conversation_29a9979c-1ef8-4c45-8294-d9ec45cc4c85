#!/usr/bin/env python3
"""
Test script cho API upload endpoint
"""

import requests
import tempfile
import os

def test_api_upload():
    """Test API upload endpoint với file tiếng Việt"""
    
    # Tạo file test
    test_content = "Đây là nội dung test với tiếng Việt 🇻🇳\nBáo cáo giá vàng hôm nay"
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
        f.write(test_content)
        temp_file_path = f.name
    
    try:
        print(f"📄 Tạo file test: {temp_file_path}")
        
        # Test upload với subfolder tiếng Việt
        with open(temp_file_path, 'rb') as f:
            files = {'file': ('test-vietnamese.txt', f, 'text/plain')}
            data = {
                'task_id': 'api_test_vietnamese',
                'subfolder': 'Báo cáo giá vàng/Hôm nay'
            }
            
            print(f"🚀 Gửi request upload...")
            print(f"   Task ID: {data['task_id']}")
            print(f"   Subfolder: {data['subfolder']}")
            
            response = requests.post(
                'http://localhost:8000/s3/upload/',
                files=files,
                data=data,
                timeout=30
            )
            
            print(f"📊 Response status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Upload thành công!")
                print(f"   📍 S3 URL: {result.get('s3_url')}")
                print(f"   🔑 S3 Key: {result.get('s3_key')}")
                print(f"   📁 S3 Bucket: {result.get('s3_bucket')}")
                print(f"   📏 File Size: {result.get('file_size')} bytes")
                print(f"   🕒 Upload Time: {result.get('upload_time')}")
                print(f"   💬 Message: {result.get('message')}")
            else:
                print(f"❌ Upload thất bại!")
                print(f"   Response: {response.text}")
    
    except Exception as e:
        print(f"❌ Lỗi khi test API: {str(e)}")
    
    finally:
        # Dọn dẹp file test
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)
            print(f"🧹 Đã xóa file test: {temp_file_path}")

def test_api_upload_no_subfolder():
    """Test API upload endpoint không có subfolder"""
    
    # Tạo file test
    test_content = "Test content without subfolder"
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
        f.write(test_content)
        temp_file_path = f.name
    
    try:
        print(f"\n📄 Tạo file test (no subfolder): {temp_file_path}")
        
        # Test upload không có subfolder
        with open(temp_file_path, 'rb') as f:
            files = {'file': ('test-no-subfolder.txt', f, 'text/plain')}
            data = {
                'task_id': 'api_test_no_subfolder'
            }
            
            print(f"🚀 Gửi request upload (no subfolder)...")
            print(f"   Task ID: {data['task_id']}")
            
            response = requests.post(
                'http://localhost:8000/s3/upload/',
                files=files,
                data=data,
                timeout=30
            )
            
            print(f"📊 Response status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Upload thành công!")
                print(f"   📍 S3 URL: {result.get('s3_url')}")
                print(f"   🔑 S3 Key: {result.get('s3_key')}")
            else:
                print(f"❌ Upload thất bại!")
                print(f"   Response: {response.text}")
    
    except Exception as e:
        print(f"❌ Lỗi khi test API: {str(e)}")
    
    finally:
        # Dọn dẹp file test
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)
            print(f"🧹 Đã xóa file test: {temp_file_path}")

def main():
    """Main function để chạy tests"""
    print("🚀 Bắt đầu test API upload endpoint")
    print("=" * 60)
    
    # Test với subfolder tiếng Việt
    test_api_upload()
    
    # Test không có subfolder
    test_api_upload_no_subfolder()
    
    print("\n" + "=" * 60)
    print("🎉 Hoàn thành tất cả API tests!")

if __name__ == "__main__":
    main() 