#!/usr/bin/env python3
"""
Application runner for Manus Crawler
"""

import uvicorn
import os
from pathlib import Path
from app.core.config import settings
from app.core.crawler import ensure_browser_works
import asyncio
import atexit
import signal
import subprocess

async def test_playwright():
    result = await ensure_browser_works()
    print(f"Playwright đang hoạt động: {result}")

asyncio.run(test_playwright())

def cleanup_on_exit():
    """Clean up Chrome processes when the application exits."""
    print("Cleaning up Chrome processes...")
    try:
        subprocess.run(["pkill", "-f", "chrome"], check=False)
        subprocess.run(["pkill", "-f", "chromium"], check=False)
        
        # Clean up lock files
        chrome_profile_dir = settings.CHROME_PROFILE_BASE_PATH
        for profile_dir in Path(chrome_profile_dir).glob("*"):
            if profile_dir.is_dir():
                for lock_file in ["SingletonLock", "SingletonCookie", "SingletonSocket", "lockfile"]:
                    lock_path = profile_dir / lock_file
                    if lock_path.exists():
                        lock_path.unlink(missing_ok=True)
    except Exception as e:
        print(f"Error during cleanup: {e}")

# Register the cleanup function
atexit.register(cleanup_on_exit)

def check_xvfb_chrome():
    print(">>> Kiểm tra cửa sổ đang mở:")
    os.system("DISPLAY=:99 xwininfo -root -tree || echo 'Không thể liệt kê cửa sổ'")
    print(">>> Kiểm tra processes:")
    os.system("ps aux | grep -E 'chrom|xvfb|openbox'")

if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        reload_dirs=["app"],
        log_level="debug"
    )
