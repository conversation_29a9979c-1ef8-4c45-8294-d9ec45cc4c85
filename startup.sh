#!/bin/bash
set -e

# Đường dẫn tới thư mục chứa Chrome profiles
CHROME_PROFILE_DIR=/app/data/chrome_profiles

# Log chi tiết
echo "🔍 Bắt đầu khởi động hệ thống..."

# Xóa lock file Xvfb với quyền root
if [ -e /tmp/.X99-lock ] || [ -e /tmp/.X11-unix/X99 ]; then
    echo "🔍 Đang xóa Xvfb lock files..."
    rm -f /tmp/.X99-lock /tmp/.X11-unix/X99
    echo "✅ Đã xóa Xvfb lock files"
fi

# Tạo function để cleanup lock files
cleanup_chrome_locks() {
    echo "🔍 Đang xóa Chrome lock files..."
    local locks=("SingletonLock" "SingletonCookie" "SingletonSocket" "lockfile" "*.lock")
    local paths=("$CHROME_PROFILE_DIR" "$CHROME_PROFILE_DIR/Network/Network Persistent State")

    # Duy<PERSON><PERSON> qua từng loại lock file và xóa
    for lock in "${locks[@]}"; do
        find $CHROME_PROFILE_DIR -name "$lock" -exec rm -f {} \;
    done

    # Duyệt qua từng thư mục cần xóa
    for path in "${paths[@]}"; do
        find $path -exec rm -f {} \;
    done

    echo "✅ Đã xóa lock files"
}

# Kill các tiến trình chung
kill_processes() {
    local process_name=$1
    echo "🔍 Đang kiểm tra $process_name processes..."
    if pgrep -f "$process_name" > /dev/null; then
        pkill -f "$process_name"
        echo "✅ Đã dọn $process_name processes"
    else
        echo "❌ Không có $process_name process nào để dừng"
    fi
}

# Kill các tiến trình Chrome
kill_chrome_processes() {
    kill_processes "chromium"
    kill_processes "chrome"
}

# Kill các tiến trình Xvfb
kill_xvfb() {
    kill_processes "Xvfb"
}

# Kiểm tra và kill X server đang chạy
echo "🔍 Kiểm tra X server..."
ps aux | grep -v grep | grep "Xvfb :99" && pkill -f "Xvfb :99" || true

# Thực hiện cleanup
cleanup_chrome_locks
kill_chrome_processes
kill_xvfb

# Đảm bảo thư mục X11 tồn tại và có quyền đúng
echo "🔧 Thiết lập X11 environment..."
mkdir -p /tmp/.X11-unix
chmod 1777 /tmp/.X11-unix
echo "✅ X11 environment đã sẵn sàng"

# Thiết lập quyền cho thư mục profile
echo "🔧 Thiết lập quyền cho Chrome profiles..."
mkdir -p $CHROME_PROFILE_DIR
chmod -R 755 $CHROME_PROFILE_DIR
mkdir -p /app/data/crawl_status
chmod -R 755 /app/data/crawl_status
echo "✅ Đã thiết lập quyền thư mục"

# Khởi động Xvfb
start_xvfb() {
    echo "🚀 Khởi động Xvfb..."

    # Nếu đã có process Xvfb đang chạy thì bỏ qua
    if pgrep -f "Xvfb :99" > /dev/null; then
        echo "⚠️ Xvfb đã đang chạy, bỏ qua việc khởi chạy lại"
        return
    fi

    rm -f /tmp/.X99-lock /tmp/.X11-unix/X99
    Xvfb :99 -screen 0 1920x1080x24 -ac &> /home/<USER>/xvfb.log &
    sleep 2

    if ! xdpyinfo -display :99 >/dev/null 2>&1; then
        echo "❌ Xvfb không khởi động được!"
        cat /home/<USER>/xvfb.log
        exit 1
    fi

    echo "✅ Xvfb đang chạy trên display :99"
}

# Khởi động Xvfb
start_xvfb

# Khởi động window manager
echo "🚀 Khởi động window manager..."
openbox &
OPENBOX_PID=$!
sleep 1
echo "✅ Window manager đã khởi động"

# Khởi động VNC server
echo "🚀 Khởi động VNC server..."
x11vnc -display :99 -forever -shared -nopw -xkb -bg -rfbport 5900 -noxdamage
sleep 2
echo "✅ VNC server đã khởi động"

# Thêm vào startup.sh trước khi chạy python run.py
echo "🔧 Cấu hình Openbox..."
mkdir -p ~/.config/openbox
cat > ~/.config/openbox/autostart << EOF
# Đặt nền màu xám để dễ nhìn
xsetroot -solid grey

# Đảm bảo tất cả cửa sổ được đưa lên trên cùng
(sleep 2 && wmctrl -a "Google" || true) &
EOF

# Kiểm tra wmctrl đã được cài đặt chưa, nếu chưa thì cài đặt
check_and_install_wmctrl() {
    echo "🔍 Kiểm tra wmctrl..."
    if [ "$(id -u)" -ne 0 ]; then
        echo "❌ Không thể cài wmctrl vì không phải root"
    else
        apt-get update && apt-get install -y wmctrl
    fi
}

# Kiểm tra và cài đặt wmctrl
check_and_install_wmctrl

# Khởi động ứng dụng chính
start_application() {
    echo "🚀 Khởi động ứng dụng chính..."
    python run.py
}

# Kiểm tra Chrome headless
check_chrome_headless() {
    echo "🔍 Kiểm tra Xvfb + Chrome..."
    if DISPLAY=:99 chromium-browser --no-sandbox --headless --screenshot=/tmp/test.png https://example.com; then
        echo "✅ Chrome headless hoạt động tốt với Xvfb"
    else
        echo "❌ Chrome headless không tạo được screenshot"
    fi
}

# Khởi động ứng dụng và kiểm tra Chrome headless
start_application
check_chrome_headless