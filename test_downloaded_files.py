#!/usr/bin/env python3
"""
Test script để kiểm tra việ<PERSON> lưu downloaded_files vào Firebase
"""

import requests
import json
import time

def test_crawl_data_structure():
    """Test cấu trúc dữ liệu crawl với downloaded_files"""
    
    # Sample crawl data giống như bạn cung cấp
    sample_crawl_data = {
        "success": True,
        "data": {
            "page_title": "Thời tiết Sài Gòn hôm nay - Manus",
            "current_task_title": "Thời tiết Sài Gòn hôm nay",
            "chat_messages": [
                {
                    "event_id": "xF3NvKpWYBc1PuXiNyjU7X",
                    "user_message": "thời tiết sài gòn hôm nay",
                    "type": "user",
                    "timestamp": ""
                },
                {
                    "event_id": "RBKoy9ZUFA6ORirPSuA624",
                    "manus_message": "Tôi đã tổng hợp thông tin thời tiết tại Thành ph<PERSON>ồ Chí <PERSON> hôm nay. Bạn xem chi tiết trong file đính kèm nhé.",
                    "type": "manus",
                    "message_subtype": "text",
                    "timestamp": "",
                    "cloudinary_files": [
                        {
                            "filename": "2025).md",
                            "s3_url": "https://storage-file-s3-v1.s3.us-east-1.amazonaws.com/crawl_results/ymtWSHPNtH67PkCQovsFJJ/Thoi_tiet_Sai_Gon_hom_nay/Thoi_tiet_Thanh_pho_Ho_Chi_Minh_hom_nay_(04/06/2025).md",
                            "s3_bucket": "storage-file-s3-v1",
                            "s3_key": "crawl_results/ymtWSHPNtH67PkCQovsFJJ/Thoi_tiet_Sai_Gon_hom_nay/Thoi_tiet_Thanh_pho_Ho_Chi_Minh_hom_nay_(04/06/2025).md",
                            "content_type": "text/markdown",
                            "file_size": 656,
                            "upload_time": "2025-06-18T22:18:56.262749",
                            "task_id": "ymtWSHPNtH67PkCQovsFJJ"
                        }
                    ]
                }
            ],
            "downloaded_files": [
                {
                    "filename": "2025).md",
                    "s3_url": "https://storage-file-s3-v1.s3.us-east-1.amazonaws.com/crawl_results/ymtWSHPNtH67PkCQovsFJJ/Thoi_tiet_Sai_Gon_hom_nay/Thoi_tiet_Thanh_pho_Ho_Chi_Minh_hom_nay_(04/06/2025).md",
                    "s3_bucket": "storage-file-s3-v1",
                    "s3_key": "crawl_results/ymtWSHPNtH67PkCQovsFJJ/Thoi_tiet_Sai_Gon_hom_nay/Thoi_tiet_Thanh_pho_Ho_Chi_Minh_hom_nay_(04/06/2025).md",
                    "content_type": "text/markdown",
                    "file_size": 656,
                    "upload_time": "2025-06-18T22:18:56.262749",
                    "subfolder": "Thời tiết Sài Gòn hôm nay/Thời tiết Thành phố Hồ Chí Minh hôm nay (04/06",
                    "task_id": "ymtWSHPNtH67PkCQovsFJJ",
                    "extracted_from_zip": "Thời tiết Sài Gòn hôm nay.zip",
                    "relative_path_in_zip": "Thời tiết Thành phố Hồ Chí Minh hôm nay (04/06/2025).md",
                    "uploaded_at": "2025-06-18T22:18:56.263109"
                },
                {
                    "type": "zip_summary",
                    "zip_filename": "Thời tiết Sài Gòn hôm nay.zip",
                    "total_extracted": 1,
                    "extracted_files": [
                        {
                            "filename": "2025).md",
                            "s3_url": "https://storage-file-s3-v1.s3.us-east-1.amazonaws.com/crawl_results/ymtWSHPNtH67PkCQovsFJJ/Thoi_tiet_Sai_Gon_hom_nay/Thoi_tiet_Thanh_pho_Ho_Chi_Minh_hom_nay_(04/06/2025).md",
                            "content_type": "text/markdown",
                            "file_size": 656
                        }
                    ],
                    "local_path": "downloads/ymtWSHPNtH67PkCQovsFJJ/Thời tiết Sài Gòn hôm nay.zip"
                }
            ],
            "task_id": "ymtWSHPNtH67PkCQovsFJJ"
        },
        "message": "Crawl thành công",
        "task_id": "ymtWSHPNtH67PkCQovsFJJ",
        "total_files": 2
    }
    
    print("📊 Sample Crawl Data Structure:")
    print(f"✅ Page Title: {sample_crawl_data['data']['page_title']}")
    print(f"✅ Current Task Title: {sample_crawl_data['data']['current_task_title']}")
    print(f"✅ Chat Messages: {len(sample_crawl_data['data']['chat_messages'])}")
    print(f"✅ Downloaded Files: {len(sample_crawl_data['data']['downloaded_files'])}")
    print(f"✅ Task ID: {sample_crawl_data['data']['task_id']}")
    print(f"✅ Total Files: {sample_crawl_data['total_files']}")
    
    # Hiển thị chi tiết downloaded_files
    print("\n📥 Downloaded Files Details:")
    for i, file_info in enumerate(sample_crawl_data['data']['downloaded_files']):
        file_type = file_info.get("type", "file")
        if file_type == "zip_summary":
            print(f"  {i+1}. [ZIP SUMMARY] {file_info.get('zip_filename')}")
            print(f"      Total extracted: {file_info.get('total_extracted')}")
            print(f"      Local path: {file_info.get('local_path')}")
        else:
            print(f"  {i+1}. [FILE] {file_info.get('filename')}")
            print(f"      Size: {file_info.get('file_size')} bytes")
            print(f"      Type: {file_info.get('content_type')}")
            print(f"      S3 URL: {file_info.get('s3_url')}")
            print(f"      Extracted from: {file_info.get('extracted_from_zip')}")
    
    return sample_crawl_data

def test_firebase_save_downloaded_files():
    """Test việc lưu downloaded_files vào Firebase"""
    
    print("\n🧪 Testing Firebase save with downloaded_files...")
    
    # Test với Firebase service trực tiếp
    try:
        import sys
        from pathlib import Path
        sys.path.insert(0, str(Path(__file__).parent))
        
        from app.utils.firebase_service import firebase_service
        from app.utils.task_scheduler import TaskScheduler
        
        # Khởi tạo Firebase
        firebase_service.initialize()
        
        if not firebase_service.is_available():
            print("❌ Firebase not available")
            return False
        
        # Tạo TaskScheduler instance để test _sanitize_crawl_data
        scheduler = TaskScheduler()
        
        # Sample data
        sample_data = {
            "page_title": "Test Page",
            "current_task_title": "Test Task",
            "downloaded_files": [
                {
                    "filename": "test.md",
                    "s3_url": "https://s3.amazonaws.com/test.md",
                    "s3_bucket": "test-bucket",
                    "s3_key": "test/test.md",
                    "content_type": "text/markdown",
                    "file_size": 1024,
                    "upload_time": "2025-06-18T22:18:56.262749",
                    "task_id": "test_task_123"
                },
                {
                    "type": "zip_summary",
                    "zip_filename": "test.zip",
                    "total_extracted": 1,
                    "extracted_files": [{"filename": "test.md"}],
                    "local_path": "downloads/test.zip"
                }
            ]
        }
        
        # Test sanitize method
        sanitized = scheduler._sanitize_crawl_data(sample_data)
        
        print("✅ Sanitized data structure:")
        print(f"  📄 Page title: {sanitized.get('page_title')}")
        print(f"  📥 Downloaded files: {len(sanitized.get('downloaded_files', []))}")
        
        for i, file_info in enumerate(sanitized.get('downloaded_files', [])):
            print(f"    {i+1}. {file_info.get('filename', file_info.get('zip_filename', 'Unknown'))}")
        
        # Test lưu vào Firebase
        import asyncio
        
        async def test_save():
            return await firebase_service.update_user_task_status(
                user_id="test_user_downloaded_files",
                task_id="test_task_downloaded_files",
                status="uploaded",
                additional_data={
                    'crawl_response': {
                        'success': True,
                        'data': sanitized
                    },
                    'test_timestamp': firebase_service._get_current_timestamp()
                }
            )
        
        result = asyncio.run(test_save())
        
        if result:
            print("✅ Successfully saved to Firebase with downloaded_files")
            return True
        else:
            print("❌ Failed to save to Firebase")
            return False
            
    except Exception as e:
        print(f"❌ Error testing Firebase save: {str(e)}")
        return False

def check_firebase_for_downloaded_files():
    """Kiểm tra Firebase xem có downloaded_files không"""
    
    print("\n🔍 Checking Firebase for downloaded_files...")
    
    try:
        # Kiểm tra qua API
        response = requests.get(
            "http://localhost:8000/debug/firebase-crawl-data/test_user_downloaded_files/test_task_downloaded_files",
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get("success"):
                task_data = data.get("task_data", {})
                crawl_response = task_data.get("crawl_response", {})
                crawl_data = crawl_response.get("data", {})
                
                downloaded_files = crawl_data.get("downloaded_files", [])
                
                print(f"✅ Found {len(downloaded_files)} downloaded_files in Firebase")
                
                for i, file_info in enumerate(downloaded_files):
                    file_type = file_info.get("type", "file")
                    if file_type == "zip_summary":
                        print(f"  {i+1}. [ZIP] {file_info.get('zip_filename')}")
                    else:
                        print(f"  {i+1}. [FILE] {file_info.get('filename')} ({file_info.get('file_size')} bytes)")
                
                return len(downloaded_files) > 0
            else:
                print(f"❌ API call failed: {data.get('error')}")
                return False
        else:
            print(f"❌ HTTP error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error checking Firebase: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 Testing Downloaded Files Storage in Firebase\n")
    
    # 1. Test data structure
    sample_data = test_crawl_data_structure()
    
    # 2. Test Firebase save
    save_success = test_firebase_save_downloaded_files()
    
    if save_success:
        # 3. Check Firebase
        time.sleep(2)  # Đợi Firebase sync
        firebase_success = check_firebase_for_downloaded_files()
        
        if firebase_success:
            print("\n🎉 SUCCESS: downloaded_files được lưu thành công vào Firebase!")
        else:
            print("\n❌ FAILED: downloaded_files không tìm thấy trong Firebase")
    else:
        print("\n❌ FAILED: Không thể lưu vào Firebase")
    
    print("\n📋 Summary:")
    print("✅ Downloaded files structure validated")
    print("✅ Firebase save method updated")
    print("✅ Debug API enhanced")
    print("✅ Test script completed")
    print("\n🎯 Next: Run full flow test to see downloaded_files in action!")
