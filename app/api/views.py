"""
Views router for serving HTML templates
"""

from fastapi import APIRouter, Request
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from pathlib import Path
import os

# Tạo templates directory - đ<PERSON><PERSON> bảo đường dẫn đúng
BASE_DIR = Path(__file__).resolve().parent.parent
templates_path = BASE_DIR / "static" / "templates"
print(f"Template path: {templates_path} - Exists: {os.path.exists(templates_path)}")

templates = Jinja2Templates(directory=str(templates_path))

# Tạo router mới
router = APIRouter()

@router.get("/", response_class=HTMLResponse)
async def index(request: Request):
    """
    Render trang chính index.html
    """
    print("Rendering index.html")
    return templates.TemplateResponse(
        name="index.html", 
        context={"request": request}
    )

@router.get("/admin", response_class=HTMLResponse)
async def admin(request: Request):
    """
    Render trang admin
    """
    return templates.TemplateResponse(
        name="admin.html", 
        context={"request": request}
    ) 