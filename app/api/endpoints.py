"""
API endpoints for Manus Crawler
"""

import os
import async<PERSON>
from typing import Dict, Any
from fastapi import APIRouter, HTTPException, Depends, Header, UploadFile, File, Form
from fastapi.responses import HTMLResponse, FileResponse
import psutil
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, BrowserContext, Page
 

from ..models.schemas import (
    CrawlUrlRequest, CrawlUrlRequestWithId,
    CrawlHtmlRequest, CrawlHtmlRequestWithId,
    SetupProfileRequest, StandardResponse,
    HealthResponse, ProfileListResponse,
    ChatWithManusRequest, CheckCrawlStatusRequest,
    CheckCrawlStatusResponse, GetUploadedFilesRequest, 
    GetUploadedFilesResponse, FileInfo,
    GetPresignedUrlRequest, GetPresignedUrlResponse,
    OpenProfileRequest
)
from ..core.crawler import crawl_manus_page_content, setup_chrome_profile_interactive, chat_with_manus_interactive, check_status_by_task, complete_profile_setup
from ..core.config import settings
from ..utils.crawl_status_manager import crawl_status_manager
from ..utils.s3_upload import s3_uploader

router = APIRouter()

# API Key validation
def verify_api_key(x_api_key: str = Header(None, alias="X-API-KEY")):
    if x_api_key != settings.ADMIN_API_KEY:
        raise HTTPException(status_code=401, detail="Invalid API Key")
    return x_api_key

@router.get("/", response_class=HTMLResponse)
async def root():
    return """
    <html>
        <head>
            <title>Manus Crawler API</title>
            <style>
                body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
                .card { background: #f8f9fa; padding: 20px; margin: 15px 0; border-radius: 8px; border-left: 4px solid #007bff; }
                .admin-card { border-left-color: #dc3545; }
                a { color: #007bff; text-decoration: none; font-weight: bold; }
                a:hover { text-decoration: underline; }
                .warning { background: #fff3cd; border-left-color: #ffc107; color: #856404; }
            </style>
        </head>
        <body>
            <h1>🕷️ Manus Crawler API</h1>
            <p>Ứng dụng FastAPI để crawl dữ liệu từ Manus.im với Playwright và realtime updates</p>

            <div class="card">
                <h3>👤 User Interface</h3>
                <p>Giao diện chính để crawl dữ liệu với realtime updates</p>
                <a href="/ui">🚀 Mở Giao diện Crawl</a>
            </div>

            <div class="card admin-card">
                <h3>🔐 Admin Panel</h3>
                <p>Quản lý Chrome profiles và cấu hình hệ thống (cần API key)</p>
                <a href="/admin">⚙️ Mở Admin Panel</a>
            </div>

            <div class="card">
                <h3>📚 API Documentation</h3>
                <p>Swagger UI với tất cả endpoints và schemas</p>
                <a href="/docs">📖 Xem API Docs</a>
            </div>

            <div class="card">
                <h3>💚 Health Check</h3>
                <p>Kiểm tra trạng thái server</p>
                <a href="/health">🔍 Health Status</a>
            </div>

            <div class="card warning">
                <h3>⚠️ Lưu ý</h3>
                <ul>
                    <li>Admin panel yêu cầu API key để truy cập</li>
                    <li>Setup Chrome profile cần tương tác thủ công</li>
                    <li>Tuân thủ robots.txt và terms of service</li>
                </ul>
            </div>
        </body>
    </html>
    """

@router.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint."""
    return HealthResponse(
        status="healthy",
        message="Manus Crawler API is running"
    )

@router.post("/test-multipart/")
async def test_multipart_upload(
    file: UploadFile = File(...),
    task_id: str = Form(...),
    subfolder: str = Form(default=None)
):
    """
    Test endpoint cho multipart upload
    """
    return {
        "success": True,
        "filename": file.filename,
        "content_type": file.content_type,
        "task_id": task_id,
        "subfolder": subfolder,
        "file_size": file.size,
        "message": "Test multipart upload works!"
    }

@router.get("/ui", response_class=HTMLResponse)
async def get_realtime_ui_page():
    """Phục vụ trang HTML cho giao diện realtime."""
    html_file_path = settings.TEMPLATES_DIR + "/index.html"
    if os.path.exists(html_file_path):
        return FileResponse(html_file_path)
    raise HTTPException(status_code=404, detail="Không tìm thấy file index.html")

@router.get("/admin", response_class=HTMLResponse)
async def get_admin_page():
    """Phục vụ trang HTML cho admin panel."""
    html_file_path = settings.TEMPLATES_DIR + "/admin.html"
    if os.path.exists(html_file_path):
        return FileResponse(html_file_path)
    raise HTTPException(status_code=404, detail="Không tìm thấy file admin.html")

@router.post("/crawl-url/", response_model=Dict[str, Any])
async def crawl_url(request: CrawlUrlRequest):
    """Crawl URL và trả về dữ liệu (không realtime)."""
    result = await crawl_manus_page_content(
        url=request.url,
        profile_name=request.profile_name,
        use_system_profile=request.use_system_profile,
        headless=request.headless
    )
    return result

@router.post("/crawl-html/", response_model=Dict[str, Any])
async def crawl_html(request: CrawlHtmlRequest):
    """Parse HTML content và trả về dữ liệu (không realtime)."""
    result = await crawl_manus_page_content(
        html_content=request.html_content,
        profile_name=request.profile_name,
        use_system_profile=request.use_system_profile,
        headless=request.headless
    )
    return result

# Admin endpoints
@router.post("/admin/setup-chrome-profile/", dependencies=[Depends(verify_api_key)])
async def admin_setup_chrome_profile(request: SetupProfileRequest):
    """
    Endpoint admin để thiết lập Chrome profile.
    Yêu cầu API Key trong header X-API-KEY.
    """
    # Import manager từ websocket module
    from .websocket import manager

    # Thiết lập profile với websocket callback để realtime updates
    result = await setup_chrome_profile_interactive(
        profile_name=request.profile_name,
        url=request.url,
        websocket_callback=lambda req_id, msg: manager.send_to_request_id(req_id, msg),
        request_id=request.profile_name  # Sử dụng profile_name làm request_id
    )
    return result

@router.post("/admin/complete-profile-setup/{profile_name}", dependencies=[Depends(verify_api_key)])
async def admin_complete_profile_setup(profile_name: str):
    """
    Endpoint admin để hoàn thành quá trình thiết lập profile.
    Được gọi khi người dùng hoàn tất thao tác trên noVNC.
    """
    # Import manager từ websocket module
    from .websocket import manager
    
    # Hoàn thành thiết lập profile với websocket callback
    result = await complete_profile_setup(
        profile_name=profile_name,
        websocket_callback=lambda req_id, msg: manager.send_to_request_id(req_id, msg),
        request_id=profile_name  # Sử dụng profile_name làm request_id
    )
    return result

@router.get("/admin/list-profiles/", response_model=ProfileListResponse, dependencies=[Depends(verify_api_key)])
async def admin_list_profiles():
    """
    Endpoint admin để liệt kê các Chrome profiles.
    Yêu cầu API Key trong header X-API-KEY.
    """
    from datetime import datetime

    chrome_profiles_path = settings.CHROME_PROFILE_BASE_PATH
    profiles = []

    try:
        if os.path.exists(chrome_profiles_path):
            for item in os.listdir(chrome_profiles_path):
                item_path = os.path.join(chrome_profiles_path, item)
                if os.path.isdir(item_path):
                    # Lấy thông tin profile
                    stat = os.stat(item_path)
                    created_time = datetime.fromtimestamp(stat.st_ctime).strftime("%Y-%m-%d %H:%M")

                    # Tính size thư mục (đơn giản)
                    total_size = 0
                    try:
                        for dirpath, dirnames, filenames in os.walk(item_path):
                            for filename in filenames:
                                filepath = os.path.join(dirpath, filename)
                                if os.path.exists(filepath):
                                    total_size += os.path.getsize(filepath)
                    except:
                        total_size = 0

                    # Chuyển đổi size sang MB
                    size_mb = round(total_size / (1024 * 1024), 1)

                    profiles.append({
                        "name": item,
                        "created": created_time,
                        "size": f"{size_mb}MB",
                        "path": item_path
                    })

        return ProfileListResponse(
            success=True,
            profiles=profiles,
            total=len(profiles),
            base_path=chrome_profiles_path
        )

    except Exception as e:
        return ProfileListResponse(
            success=False,
            error=str(e),
            profiles=[],
            total=0
        )

@router.delete("/admin/delete-profile/{profile_name}", dependencies=[Depends(verify_api_key)])
async def admin_delete_profile(profile_name: str):
    """
    Endpoint admin để xóa Chrome profile.
    Yêu cầu API Key trong header X-API-KEY.
    """
    import shutil

    chrome_profiles_path = settings.CHROME_PROFILE_BASE_PATH
    profile_path = os.path.join(chrome_profiles_path, profile_name)

    try:
        if os.path.exists(profile_path) and os.path.isdir(profile_path):
            shutil.rmtree(profile_path)
            return {
                "success": True,
                "message": f"Profile '{profile_name}' đã được xóa thành công",
                "profile_name": profile_name
            }
        else:
            return {
                "success": False,
                "message": f"Profile '{profile_name}' không tồn tại",
                "profile_name": profile_name
            }
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": f"Lỗi khi xóa profile '{profile_name}': {str(e)}",
            "profile_name": profile_name
        }

@router.post("/admin/open-profile/", dependencies=[Depends(verify_api_key)])
async def admin_open_profile(request: OpenProfileRequest):
    profile_path = os.path.join(settings.CHROME_PROFILE_BASE_PATH, request.profile_name)
    unlock_chrome_profile(profile_path)
    playwright = await async_playwright().start()
    browser = await playwright.chromium.launch_persistent_context(
        user_data_dir=profile_path,
        headless=False,
        viewport=None,
        ignore_default_args=["--enable-automation"],
        chromium_sandbox=False,
        args=[
            "--no-sandbox", 
            "--disable-setuid-sandbox",
            "--disable-dev-shm-usage",
            f"--display={os.environ.get('DISPLAY', ':99')}",
            "--disable-blink-features=AutomationControlled",
            "--window-position=0,0",
            "--window-size=1600,900",
            "--no-first-run",
            "--start-maximized",
            "--disable-extensions"
        ]
    )

    return {"success": True, "message": "Profile đã được mở", "profile_name": request.profile_name}

# router close profile
@router.post("/chat-with-manus-realtime/")
async def chat_with_manus_realtime(request: ChatWithManusRequest):
    """
    Gửi message lên Manus.im và crawl response realtime
    """
    try:
        # Import manager từ websocket module
        from .websocket import manager

        # Bắt đầu chat session
        result = await chat_with_manus_interactive(
            message=request.message,
            task_url=request.task_url,
            profile_name=request.profile_name,
            headless=request.headless,
            websocket_callback=lambda req_id, msg: manager.send_to_request_id(req_id, msg),
            request_id=request.request_id
        )

        print("result chat with manus interactive")
        print(result)

        # Gửi kết quả qua WebSocket
        if result["success"]:
            await manager.send_data_to_request_id(request.request_id, result)
        else:
            await manager.send_error_to_request_id(request.request_id, result.get("error", "Unknown error"))

        return {"status": "started", "request_id": request.request_id, "task_id": result.get("task_id", None)}
    except Exception as e:
        # Import manager nếu chưa có
        try:
            from .websocket import manager
            await manager.send_error_to_request_id(request.request_id, str(e))
        except:
            pass
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/check-crawl-status/", response_model=CheckCrawlStatusResponse)
async def check_crawl_status(request: CheckCrawlStatusRequest):
    """
    Check the status of a crawl task by task ID và trả về danh sách file uploads.
    """
    try:
        from datetime import datetime
        from .websocket import manager

        # Check profile_name có tồn tại không
        if request.profile_name:
            chrome_profiles_path = settings.CHROME_PROFILE_BASE_PATH
            profile_path = os.path.join(chrome_profiles_path, request.profile_name)

            if not os.path.exists(profile_path) or not os.path.isdir(profile_path):
                return CheckCrawlStatusResponse(
                    success=False,
                    task_id=request.task_id,
                    status="error",
                    error=f"Profile '{request.profile_name}' does not exist"
                )

        # Lấy thông tin task từ status manager
        task_status = await crawl_status_manager.get_task_status(request.task_id)
        
        if task_status:
            return CheckCrawlStatusResponse(
                success=True,
                task_id=request.task_id,
                status=task_status["status"],
                message=f"Task {request.task_id} is {task_status['status']}",
                last_updated=task_status["updated_at"],
                uploaded_files=task_status.get("uploaded_files", [])
            )
        else:
            # Fallback to original logic if task not found in status manager
            result = await check_status_by_task(
                task_id=request.task_id,
                profile_name=request.profile_name,
                headless=request.headless,
                websocket_callback=lambda req_id, msg: manager.send_to_request_id(req_id, msg),
            )

            if result["success"]:
                return CheckCrawlStatusResponse(
                    success=True,
                    task_id=request.task_id,
                    status=result['status'],
                    message=f"Task {request.task_id} is {result['status']}",
                    last_updated=datetime.now().isoformat()
                )
            else:
                return CheckCrawlStatusResponse(
                    success=False,
                    task_id=request.task_id,
                    status="error",
                    error="Task not found or invalid"
                )

    except Exception as e:
        return CheckCrawlStatusResponse(
            success=False,
            task_id=request.task_id,
            status="error",
            error=str(e)
        )

@router.post("/get-uploaded-files/", response_model=GetUploadedFilesResponse)
async def get_uploaded_files(request: GetUploadedFilesRequest):
    """
    Lấy danh sách file đã upload của một task cụ thể.
    """
    try:
        # Lấy thông tin task từ status manager
        task_status = await crawl_status_manager.get_task_status(request.task_id)
        
        if not task_status:
            return GetUploadedFilesResponse(
                success=False,
                task_id=request.task_id,
                error="Task not found"
            )
        
        uploaded_files = task_status.get("uploaded_files", [])
        
        # Convert to FileInfo objects
        file_list = []
        for file_data in uploaded_files:
            file_info = FileInfo(
                filename=file_data.get("filename", ""),
                # S3 fields
                s3_url=file_data.get("s3_url"),
                s3_bucket=file_data.get("s3_bucket"),
                s3_key=file_data.get("s3_key"),
                content_type=file_data.get("content_type"),
                file_size=file_data.get("file_size"),
                # Backwards compatibility với Cloudinary
                cloudinary_url=file_data.get("cloudinary_url"),
                public_id=file_data.get("public_id"),
                resource_type=file_data.get("resource_type"),
                format=file_data.get("format"),
                bytes=file_data.get("bytes"),
                upload_time=file_data.get("upload_time"),
                uploaded_at=file_data.get("uploaded_at"),
                local_path=file_data.get("local_path"),
                upload_error=file_data.get("upload_error"),
                # Additional fields
                type=file_data.get("type"),
                extracted_from_zip=file_data.get("extracted_from_zip"),
                relative_path_in_zip=file_data.get("relative_path_in_zip"),
                subfolder=file_data.get("subfolder"),
                total_extracted=file_data.get("total_extracted"),
                extracted_files=file_data.get("extracted_files"),
                task_id=file_data.get("task_id")
            )
            file_list.append(file_info)
        
        return GetUploadedFilesResponse(
            success=True,
            task_id=request.task_id,
            files=file_list,
            total_files=len(file_list),
            task_status=task_status["status"]
        )
        
    except Exception as e:
        return GetUploadedFilesResponse(
            success=False,
            task_id=request.task_id,
            error=str(e)
        )

@router.post("/admin/cleanup-old-tasks/", dependencies=[Depends(verify_api_key)])
async def admin_cleanup_old_tasks(days: int = 7):
    """
    Endpoint admin để dọn dẹp các task cũ.
    Yêu cầu API Key trong header X-API-KEY.
    """
    try:
        deleted_count = await crawl_status_manager.cleanup_old_tasks(days)
        
        return {
            "success": True,
            "message": f"Đã xóa {deleted_count} task cũ hơn {days} ngày",
            "deleted_count": deleted_count,
            "days": days
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": f"Lỗi khi cleanup tasks: {str(e)}"
        }

# S3 Upload endpoints
@router.post("/s3/get-presigned-url/", response_model=GetPresignedUrlResponse)
async def get_presigned_url(request: GetPresignedUrlRequest):
    """
    Tạo presigned URL để upload file trực tiếp lên S3 từ frontend
    """
    try:
        # Tạo S3 key từ request
        s3_key = s3_uploader._get_s3_key(request.task_id, 
                                        request.key.split('/')[-1],  # Extract filename
                                        request.key.split('/')[0] if '/' in request.key else None)  # Extract subfolder
        
        # Generate presigned URL
        presigned_data = await s3_uploader.generate_presigned_url(
            s3_key=s3_key,
            content_type=request.content_type
        )
        
        if presigned_data:
            return GetPresignedUrlResponse(
                success=True,
                presigned_url=presigned_data['presigned_url'],
                fields=presigned_data['fields']
            )
        else:
            return GetPresignedUrlResponse(
                success=False,
                error="Failed to generate presigned URL. S3 may not be configured."
            )
            
    except Exception as e:
        return GetPresignedUrlResponse(
            success=False,
            error=str(e)
        )

@router.post("/upload-file/")
async def direct_upload_file(
    file: UploadFile = File(...),
    task_id: str = Form(...),
    subfolder: str = Form(default=None)
):
    """
    Upload file trực tiếp lên S3 thông qua backend
    """
    try:
        # Đọc file content
        file_content = await file.read()
        
        # Upload file sử dụng S3Uploader
        upload_result = await s3_uploader.upload_file_bytes(
            file_bytes=file_content,
            filename=file.filename,
            task_id=task_id,
            subfolder=subfolder,
            content_type=file.content_type
        )
        
        if upload_result:
            # Tạo task nếu chưa có
            task_status = await crawl_status_manager.get_task_status(task_id)
            if not task_status:
                await crawl_status_manager.create_task(task_id, {"source": "direct_upload"})
            
            # Cập nhật task status với file đã upload
            await crawl_status_manager.add_uploaded_file(task_id, upload_result)
            
            return {
                "success": True,
                "filename": upload_result["filename"],
                "s3_url": upload_result["s3_url"],
                "s3_bucket": upload_result["s3_bucket"],
                "s3_key": upload_result["s3_key"],
                "file_size": upload_result["file_size"],
                "content_type": upload_result["content_type"],
                "upload_time": upload_result["upload_time"],
                "message": "File uploaded successfully"
            }
        else:
            return {
                "success": False,
                "error": "Failed to upload file to S3"
            }
            
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

@router.get("/s3/list-files/{task_id}")
async def list_s3_files(task_id: str, prefix: str = None):
    """
    Liệt kê các file của một task trên S3
    """
    try:
        files = await s3_uploader.list_files(task_id, prefix)
        
        return {
            "success": True,
            "task_id": task_id,
            "files": files,
            "total_files": len(files)
        }
        
    except Exception as e:
        return {
            "success": False,
            "task_id": task_id,
            "error": str(e)
        }


def unlock_chrome_profile(profile_path):
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                cmdline = ' '.join(proc.info['cmdline'])
                if profile_path in cmdline:
                    print(f"Killing process {proc.pid}: {cmdline}")
                    proc.kill()
        except Exception as e:
            print(f"Error killing process: {e}")

    lock_files = ["SingletonLock", "LOCK"]
    for fname in lock_files:
        fpath = os.path.join(profile_path, fname)
        if os.path.exists(fpath):
            try:
                os.remove(fpath)
            except Exception as e:
                logging.warning(f"Failed to remove {fpath}: {str(e)}")