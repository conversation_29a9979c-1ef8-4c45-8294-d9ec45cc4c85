"""
WebSocket handlers for real-time communication
"""

import asyncio
from typing import Dict, List, Any, Callable, Awaitable
from fastapi import APIRouter, WebSocket, WebSocketDisconnect

from ..models.schemas import WebSocketMessage, CrawlUrlRequestWithId, CrawlHtmlRequestWithId
from ..core.crawler import crawl_manus_page_content
import os
from app.core.config import settings
import json
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, BrowserContext, Page
import signal
import psutil
import shutil
import logging

router = APIRouter()

# WebSocket Connection Manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, List[WebSocket]] = {}

    async def connect(self, websocket: WebSocket, request_id: str):
        if request_id not in self.active_connections:
            self.active_connections[request_id] = []
        self.active_connections[request_id].append(websocket)

    def disconnect(self, websocket: WebSocket, request_id: str):
        if request_id in self.active_connections:
            if websocket in self.active_connections[request_id]:
                self.active_connections[request_id].remove(websocket)
            if not self.active_connections[request_id]:
                del self.active_connections[request_id]

    async def send_to_request_id(self, request_id: str, message: str):
        if request_id in self.active_connections:
            websocket_message = WebSocketMessage(type="progress", message=message)
            message_json = websocket_message.model_dump_json()
            
            disconnected_websockets = []
            for websocket in self.active_connections[request_id]:
                try:
                    await websocket.send_text(message_json)
                except:
                    disconnected_websockets.append(websocket)
            
            # Xóa các websocket đã disconnect
            for ws in disconnected_websockets:
                self.disconnect(ws, request_id)

    async def send_data_to_request_id(self, request_id: str, data: Dict[str, Any]):
        if request_id in self.active_connections:
            websocket_message = WebSocketMessage(type="data", data=data)
            message_json = websocket_message.model_dump_json()
            
            disconnected_websockets = []
            for websocket in self.active_connections[request_id]:
                try:
                    await websocket.send_text(message_json)
                except:
                    disconnected_websockets.append(websocket)
            
            # Xóa các websocket đã disconnect
            for ws in disconnected_websockets:
                self.disconnect(ws, request_id)

    async def send_error_to_request_id(self, request_id: str, error_message: str):
        if request_id in self.active_connections:
            websocket_message = WebSocketMessage(type="error", message=error_message)
            message_json = websocket_message.model_dump_json()
            
            disconnected_websockets = []
            for websocket in self.active_connections[request_id]:
                try:
                    await websocket.send_text(message_json)
                except:
                    disconnected_websockets.append(websocket)
            
            # Xóa các websocket đã disconnect
            for ws in disconnected_websockets:
                self.disconnect(ws, request_id)

# Global connection manager
manager = ConnectionManager()

@router.websocket("/ws/crawl-status/{request_id}")
async def websocket_endpoint(websocket: WebSocket, request_id: str):
    await manager.connect(websocket, request_id)
    try:
        while True:
            # Giữ kết nối mở
            await websocket.receive_text()
    except WebSocketDisconnect:
        manager.disconnect(websocket, request_id)

@router.websocket("/ws/{profile_name}")
async def websocket_profile_setup(websocket: WebSocket, profile_name: str):
    await websocket.accept()
    await manager.connect(websocket, profile_name)
    
    try:
        profile_path = os.path.join(settings.CHROME_PROFILE_BASE_PATH, profile_name)
        unlock_chrome_profile(profile_path)
        playwright = await async_playwright().start()
        browser = await playwright.chromium.launch_persistent_context(
            user_data_dir=profile_path,
            headless=False,
            viewport=None,
            ignore_default_args=["--enable-automation"],
            chromium_sandbox=False,
            args=[
                "--no-sandbox", 
                "--disable-setuid-sandbox",
                "--disable-dev-shm-usage",
                f"--display={os.environ.get('DISPLAY', ':99')}",
                "--disable-blink-features=AutomationControlled",
                "--window-position=0,0",
                "--window-size=1600,900",
                "--no-first-run",
                "--start-maximized",
                "--disable-extensions"
            ]
        )

        await manager.send_to_request_id(
            profile_name,
            WebSocketMessage(
                type="progress", 
                message=f"✅ Đã khởi chạy trình duyệt cho profile '{profile_name}'"
            ).model_dump_json()  # <-- Sử dụng method này thay vì json.dumps
        )

        # Giữ kết nối WebSocket
        while True:
            await websocket.receive_text()

    except WebSocketDisconnect:
        try:
            await asyncio.wait_for(browser.close(), timeout=10)
            await playwright.stop()
        except asyncio.TimeoutError:
            logging.error("Timeout when closing browser")
        finally:
            manager.disconnect(websocket, profile_name)
            await manager.send_to_request_id(
                profile_name,
                WebSocketMessage(
                    type="error", 
                    message=f"🔌 Đã đóng kết nối với profile '{profile_name}'"
                ).model_dump_json()
            )

# Realtime crawl endpoints
@router.post("/crawl-url-realtime/")
async def crawl_url_realtime(request: CrawlUrlRequestWithId):
    """Crawl URL với cập nhật realtime qua WebSocket."""
    
    async def websocket_callback(request_id: str, message: str):
        await manager.send_to_request_id(request_id, message)
    
    # Chạy crawl trong background task
    async def run_crawl():
        result = await crawl_manus_page_content(
            url=request.url,
            profile_name=request.profile_name,
            use_system_profile=request.use_system_profile,
            headless=request.headless,
            websocket_callback=websocket_callback,
            request_id=request.request_id
        )
        
        if result["success"]:
            await manager.send_data_to_request_id(request.request_id, result["data"])
        else:
            await manager.send_error_to_request_id(request.request_id, result.get("message", "Unknown error"))
        
        return result
    
    # Khởi chạy task trong background
    asyncio.create_task(run_crawl())
    
    return {"message": "Crawl started", "request_id": request.request_id}

@router.post("/crawl-html-realtime/")
async def crawl_html_realtime(request: CrawlHtmlRequestWithId):
    """Parse HTML content với cập nhật realtime qua WebSocket."""
    
    async def websocket_callback(request_id: str, message: str):
        await manager.send_to_request_id(request_id, message)
    
    # Chạy crawl trong background task
    async def run_crawl():
        result = await crawl_manus_page_content(
            html_content=request.html_content,
            profile_name=request.profile_name,
            use_system_profile=request.use_system_profile,
            headless=request.headless,
            websocket_callback=websocket_callback,
            request_id=request.request_id
        )
        
        if result["success"]:
            await manager.send_data_to_request_id(request.request_id, result["data"])
        else:
            await manager.send_error_to_request_id(request.request_id, result.get("message", "Unknown error"))
        
        return result
    
    # Khởi chạy task trong background
    asyncio.create_task(run_crawl())
    
    return {"message": "HTML parsing started", "request_id": request.request_id}
