"""
Firebase middleware để track API calls và lưu vào Realtime Database
"""

import time
import json
from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import StreamingResponse
from ..utils.firebase_service import firebase_service

class FirebaseLoggingMiddleware(BaseHTTPMiddleware):
    """
    Middleware để log tất cả API calls vào Firebase Realtime Database
    """
    
    def __init__(self, app, exclude_paths: list = None):
        super().__init__(app)
        # Các path không cần log (static files, health check, etc.)
        self.exclude_paths = exclude_paths or [
            "/static",
            "/favicon.ico",
            "/health",
            "/docs",
            "/openapi.json",
            "/redoc"
        ]
        
        # Khởi tạo Firebase service
        firebase_service.initialize()
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Xử lý request và log vào Firebase
        """
        # Kiểm tra xem có cần log path này không
        if self._should_exclude_path(request.url.path):
            return await call_next(request)
        
        # Bắt đầu đo thời gian
        start_time = time.time()
        
        # L<PERSON>y thông tin request
        user_ip = self._get_client_ip(request)
        user_id = self._extract_user_id(request)
        
        # Đọc request body (nếu có)
        request_data = await self._get_request_data(request)
        
        # Gọi endpoint
        response = await call_next(request)
        
        # Tính thời gian thực thi
        execution_time = time.time() - start_time
        
        # Lấy response data
        response_data = await self._get_response_data(response)
        
        # Log vào Firebase (async, không block response)
        try:
            await firebase_service.log_api_call(
                endpoint=request.url.path,
                method=request.method,
                user_id=user_id,
                user_ip=user_ip,
                request_data=request_data,
                response_data=response_data,
                status_code=response.status_code,
                execution_time=execution_time,
                error=None if response.status_code < 400 else f"HTTP {response.status_code}"
            )
        except Exception as e:
            # Không để lỗi Firebase ảnh hưởng đến response
            print(f"⚠️ Firebase logging error: {str(e)}")
        
        return response
    
    def _should_exclude_path(self, path: str) -> bool:
        """
        Kiểm tra xem path có nên được loại trừ khỏi logging không
        """
        return any(path.startswith(exclude_path) for exclude_path in self.exclude_paths)
    
    def _get_client_ip(self, request: Request) -> str:
        """
        Lấy IP address của client
        """
        # Kiểm tra các header proxy thông thường
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # Fallback to client host
        if hasattr(request, "client") and request.client:
            return request.client.host
        
        return "unknown"
    
    def _extract_user_id(self, request: Request) -> str:
        """
        Trích xuất user ID từ request
        Có thể từ header, query param, hoặc JWT token
        """
        # Kiểm tra header X-User-ID
        user_id = request.headers.get("X-User-ID")
        if user_id:
            return user_id
        
        # Kiểm tra query parameter
        user_id = request.query_params.get("user_id")
        if user_id:
            return user_id
        
        # Có thể thêm logic để extract từ JWT token
        # auth_header = request.headers.get("Authorization")
        # if auth_header and auth_header.startswith("Bearer "):
        #     token = auth_header[7:]
        #     # Decode JWT và lấy user_id
        
        return None
    
    async def _get_request_data(self, request: Request) -> dict:
        """
        Lấy dữ liệu từ request body
        """
        try:
            # Chỉ đọc body cho POST, PUT, PATCH requests
            if request.method in ["POST", "PUT", "PATCH"]:
                content_type = request.headers.get("content-type", "")
                
                if "application/json" in content_type:
                    # Đọc JSON body
                    body = await request.body()
                    if body:
                        return json.loads(body.decode())
                elif "multipart/form-data" in content_type:
                    # Cho multipart form, chỉ log metadata
                    return {"type": "multipart_form_data"}
                elif "application/x-www-form-urlencoded" in content_type:
                    # Cho form data, chỉ log metadata
                    return {"type": "form_data"}
            
            # Log query parameters
            if request.query_params:
                return {"query_params": dict(request.query_params)}
            
            return None
            
        except Exception as e:
            print(f"⚠️ Error reading request data: {str(e)}")
            return {"error": "failed_to_read_request"}
    
    async def _get_response_data(self, response: Response) -> dict:
        """
        Lấy dữ liệu từ response
        """
        try:
            # Chỉ log response data cho JSON responses
            content_type = response.headers.get("content-type", "")
            
            if "application/json" in content_type:
                # Đối với StreamingResponse, không thể đọc body
                if isinstance(response, StreamingResponse):
                    return {"type": "streaming_response"}
                
                # Đối với Response thông thường
                if hasattr(response, 'body') and response.body:
                    try:
                        body_str = response.body.decode() if isinstance(response.body, bytes) else str(response.body)
                        return json.loads(body_str)
                    except:
                        return {"type": "json_response", "size": len(response.body) if response.body else 0}
            
            return {
                "content_type": content_type,
                "status_code": response.status_code
            }
            
        except Exception as e:
            print(f"⚠️ Error reading response data: {str(e)}")
            return {"error": "failed_to_read_response"}
