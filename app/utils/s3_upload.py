import os
import asyncio
import aiofiles
import zipfile
import tempfile
import shutil
import mimetypes
import unidecode
from typing import Dict, Any, Optional, List
from pathlib import Path
from datetime import datetime

import boto3
from botocore.exceptions import NoCredentialsError, ClientError


class S3Uploader:
    """
    Class để upload file lên AWS S3 thay thế Cloudinary
    """
    
    def __init__(self):
        # <PERSON><PERSON><PERSON> thông tin S3 từ environment variables
        self.aws_access_key_id = os.getenv('AWS_ACCESS_KEY_ID')
        self.aws_secret_access_key = os.getenv('AWS_SECRET_ACCESS_KEY')
        self.aws_region = os.getenv('AWS_REGION', 'ap-southeast-1')
        self.bucket_name = os.getenv('S3_BUCKET_NAME')
        
        # Khởi tạo S3 client nếu có đầy đủ credentials
        self.s3_client = None
        if all([self.aws_access_key_id, self.aws_secret_access_key, self.bucket_name]):
            try:
                self.s3_client = boto3.client(
                    's3',
                    aws_access_key_id=self.aws_access_key_id,
                    aws_secret_access_key=self.aws_secret_access_key,
                    region_name=self.aws_region
                )
                print(f"✅ S3Uploader initialized with bucket: {self.bucket_name}")
            except Exception as e:
                print(f"⚠️ Failed to initialize S3 client: {str(e)}")
                self.s3_client = None
        else:
            print("⚠️ S3 credentials not found. S3 upload will be disabled. Please set: AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, S3_BUCKET_NAME")
    
    def _check_s3_available(self) -> bool:
        """Kiểm tra xem S3 client có sẵn sàng không"""
        return self.s3_client is not None
    
    def _get_content_type(self, file_path: str) -> str:
        """
        Lấy content type của file dựa trên extension
        
        Args:
            file_path: Đường dẫn file
            
        Returns:
            Content type string
        """
        content_type, _ = mimetypes.guess_type(file_path)
        return content_type or 'application/octet-stream'
    
    def _sanitize_metadata_value(self, value: str) -> str:
        """
        Chuẩn hóa giá trị metadata để chỉ chứa ASCII characters
        
        Args:
            value: Giá trị cần chuẩn hóa
            
        Returns:
            Giá trị đã được chuẩn hóa thành ASCII
        """
        try:
            # Sử dụng unidecode để chuyển đổi unicode thành ASCII
            return unidecode.unidecode(value)
        except:
            # Fallback: chỉ giữ lại ASCII characters
            return ''.join(char for char in value if ord(char) < 128)
    
    def _get_s3_key(self, task_id: str, filename: str, subfolder: str = None) -> str:
        """
        Tạo S3 key cho file với cấu trúc folder theo task ID
        Sanitize tên file và subfolder để tránh vấn đề với ký tự đặc biệt
        
        Args:
            task_id: ID của task
            filename: Tên file
            subfolder: Subfolder bên trong task folder
            
        Returns:
            S3 key string đã được sanitize
        """
        # Cấu trúc: crawl_results/{task_id}/{subfolder}/{filename}
        key_parts = ['crawl_results', task_id]
        
        if subfolder:
            # Sanitize subfolder để tránh vấn đề với ký tự tiếng Việt và ký tự đặc biệt
            sanitized_subfolder = self._sanitize_path_component(subfolder)
            key_parts.append(sanitized_subfolder)
            
        # Sanitize filename để tránh vấn đề với ký tự tiếng Việt và ký tự đặc biệt
        sanitized_filename = self._sanitize_path_component(filename)
        key_parts.append(sanitized_filename)
        
        return '/'.join(key_parts)

    def _sanitize_path_component(self, path_component: str) -> str:
        """
        Sanitize một thành phần path để phù hợp với S3 key requirements
        
        Args:
            path_component: Thành phần path cần sanitize
            
        Returns:
            Thành phần path đã được sanitize
        """
        try:
            # Bước 1: Chuyển đổi unicode thành ASCII bằng unidecode
            ascii_component = unidecode.unidecode(path_component)
            
            # Bước 2: Thay thế các ký tự không phù hợp bằng dấu gạch dưới
            # S3 không khuyến khích một số ký tự trong key
            invalid_chars = ['\\', '{', '^', '}', '%', '`', ']', '"', '>', '[', '~', '<', '#', '|']
            sanitized = ascii_component
            for char in invalid_chars:
                sanitized = sanitized.replace(char, '_')
            
            # Bước 3: Thay thế khoảng trắng bằng dấu gạch dưới để tránh vấn đề URL encoding
            sanitized = sanitized.replace(' ', '_')
            
            # Bước 4: Loại bỏ dấu gạch dưới liên tiếp
            import re
            sanitized = re.sub(r'_+', '_', sanitized)
            
            # Bước 5: Loại bỏ dấu gạch dưới ở đầu và cuối
            sanitized = sanitized.strip('_')
            
            # Bước 6: Đảm bảo không rỗng
            if not sanitized:
                sanitized = 'unnamed'
                
            return sanitized
            
        except Exception as e:
            print(f"⚠️ Lỗi khi sanitize path component '{path_component}': {str(e)}")
            # Fallback: chỉ giữ lại ASCII characters và thay khoảng trắng
            return ''.join(char if char.isalnum() else '_' for char in path_component).strip('_') or 'unnamed'
    
    async def upload_file(self, file_path: str, task_id: str, subfolder: str = None) -> Optional[Dict[str, Any]]:
        """
        Upload file lên S3
        
        Args:
            file_path: Đường dẫn file local
            task_id: ID của task để tổ chức file
            subfolder: Subfolder bên trong task folder (cho file từ zip)
            
        Returns:
            Dict chứa thông tin file upload hoặc None nếu lỗi
        """
        try:
            # Kiểm tra S3 client có sẵn sàng không
            if not self._check_s3_available():
                print(f"❌ S3 client not available. Cannot upload file: {file_path}")
                return None
            
            if not os.path.exists(file_path):
                print(f"File không tồn tại: {file_path}")
                return None
            
            filename = os.path.basename(file_path)
            s3_key = self._get_s3_key(task_id, filename, subfolder)
            content_type = self._get_content_type(file_path)
            
            # Lấy file size
            file_size = os.path.getsize(file_path)
            
            # Upload file lên S3 (chạy trong thread pool để không block)
            loop = asyncio.get_event_loop()
            
            def upload_to_s3():
                # Chuẩn hóa metadata để chỉ chứa ASCII characters
                metadata = {
                    'task_id': task_id,
                    'uploaded_at': datetime.now().isoformat(),
                    'original_filename': self._sanitize_metadata_value(filename)
                }
                
                if subfolder:
                    # Chuyển đổi subfolder thành ASCII an toàn
                    metadata['subfolder'] = self._sanitize_metadata_value(subfolder)
                
                extra_args = {
                    'ContentType': content_type,
                    'Metadata': metadata
                }
                
                self.s3_client.upload_file(
                    file_path,
                    self.bucket_name,
                    s3_key,
                    ExtraArgs=extra_args
                )
                
                return {
                    'bucket': self.bucket_name,
                    'key': s3_key,
                    'url': f"https://{self.bucket_name}.s3.{self.aws_region}.amazonaws.com/{s3_key}"
                }
            
            # Upload trong executor
            upload_result = await loop.run_in_executor(None, upload_to_s3)
            
            print(f"✅ Upload thành công: {filename} -> {upload_result['url']}")
            
            return {
                "filename": filename,
                "s3_url": upload_result['url'],
                "s3_bucket": upload_result['bucket'],
                "s3_key": upload_result['key'],
                "content_type": content_type,
                "file_size": file_size,
                "upload_time": datetime.now().isoformat(),
                "subfolder": subfolder,
                "task_id": task_id
            }
                        
        except NoCredentialsError:
            print(f"❌ AWS credentials không hợp lệ")
            return None
        except ClientError as e:
            print(f"❌ AWS S3 error: {str(e)}")
            return None
        except Exception as e:
            print(f"❌ Lỗi upload file {file_path}: {str(e)}")
            return None

    async def upload_file_bytes(self, file_bytes: bytes, filename: str, task_id: str, subfolder: str = None, content_type: str = None) -> Optional[Dict[str, Any]]:
        """
        Upload file từ bytes trực tiếp lên S3
        
        Args:
            file_bytes: File content dưới dạng bytes
            filename: Tên file
            task_id: ID của task
            subfolder: Subfolder bên trong task folder
            content_type: Content type của file
            
        Returns:
            Dict chứa thông tin file upload hoặc None nếu lỗi
        """
        try:
            # Kiểm tra S3 client có sẵn sàng không
            if not self._check_s3_available():
                print(f"❌ S3 client not available. Cannot upload file: {filename}")
                return None
            
            s3_key = self._get_s3_key(task_id, filename, subfolder)
            
            # Tự động detect content type nếu không có
            if not content_type:
                content_type = self._get_content_type(filename)
            
            # Lấy file size
            file_size = len(file_bytes)
            
            # Upload file lên S3 (chạy trong thread pool để không block)
            loop = asyncio.get_event_loop()
            
            def upload_to_s3():
                # Chuẩn hóa metadata để chỉ chứa ASCII characters
                metadata = {
                    'task_id': task_id,
                    'uploaded_at': datetime.now().isoformat(),
                    'original_filename': self._sanitize_metadata_value(filename)
                }
                
                if subfolder:
                    # Chuyển đổi subfolder thành ASCII an toàn
                    metadata['subfolder'] = self._sanitize_metadata_value(subfolder)
                
                extra_args = {
                    'ContentType': content_type,
                    'Metadata': metadata
                }
                
                # Upload từ bytes
                import io
                file_obj = io.BytesIO(file_bytes)
                
                self.s3_client.upload_fileobj(
                    file_obj,
                    self.bucket_name,
                    s3_key,
                    ExtraArgs=extra_args
                )
                
                return {
                    'bucket': self.bucket_name,
                    'key': s3_key,
                    'url': f"https://{self.bucket_name}.s3.{self.aws_region}.amazonaws.com/{s3_key}"
                }
            
            # Upload trong executor
            upload_result = await loop.run_in_executor(None, upload_to_s3)
            
            print(f"✅ Upload bytes thành công: {filename} -> {upload_result['url']}")
            
            return {
                "filename": filename,
                "s3_url": upload_result['url'],
                "s3_bucket": upload_result['bucket'],
                "s3_key": upload_result['key'],
                "content_type": content_type,
                "file_size": file_size,
                "upload_time": datetime.now().isoformat(),
                "subfolder": subfolder,
                "task_id": task_id
            }
                        
        except NoCredentialsError:
            print(f"❌ AWS credentials không hợp lệ")
            return None
        except ClientError as e:
            print(f"❌ AWS S3 error: {str(e)}")
            return None
        except Exception as e:
            print(f"❌ Lỗi upload file bytes {filename}: {str(e)}")
            return None

    async def extract_and_upload_zip(self, zip_path: str, task_id: str) -> List[Dict[str, Any]]:
        """
        Giải nén file zip và upload từng file lên S3
        
        Args:
            zip_path: Đường dẫn file zip
            task_id: ID của task
            
        Returns:
            List các Dict chứa thông tin các file đã upload
        """
        uploaded_files = []
        temp_extract_dir = None
        
        try:
            # Tạo thư mục tạm để giải nén
            temp_extract_dir = tempfile.mkdtemp(prefix=f"extract_{task_id}_")
            print(f"📦 Giải nén zip file: {zip_path} -> {temp_extract_dir}")
            
            # Giải nén file zip
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(temp_extract_dir)
            
            # Lấy tên zip file (không có extension) để làm subfolder
            zip_filename = Path(zip_path).stem
            
            # Upload từng file trong thư mục giải nén
            for root, dirs, files in os.walk(temp_extract_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    
                    # Tạo relative path từ extract dir để làm subfolder
                    relative_path = os.path.relpath(root, temp_extract_dir)
                    if relative_path == ".":
                        # File ở root của zip
                        subfolder = zip_filename
                    else:
                        # File ở subfolder của zip
                        subfolder = f"{zip_filename}/{relative_path}"
                    
                    # Upload file
                    upload_result = await self.upload_file(file_path, task_id, subfolder)
                    
                    if upload_result:
                        # Thêm thông tin về original zip
                        upload_result["extracted_from_zip"] = os.path.basename(zip_path)
                        upload_result["relative_path_in_zip"] = os.path.relpath(file_path, temp_extract_dir)
                        uploaded_files.append(upload_result)
                        print(f"  ✅ Uploaded extracted file: {file}")
                    else:
                        print(f"  ❌ Failed to upload extracted file: {file}")
            
            print(f"✅ Hoàn thành giải nén và upload {len(uploaded_files)} files từ zip")
            
        except zipfile.BadZipFile:
            print(f"❌ File không phải zip hợp lệ: {zip_path}")
        except Exception as e:
            print(f"❌ Lỗi khi giải nén và upload zip: {str(e)}")
        finally:
            # Dọn dẹp thư mục tạm
            if temp_extract_dir and os.path.exists(temp_extract_dir):
                try:
                    shutil.rmtree(temp_extract_dir)
                    print(f"🧹 Đã dọn dẹp thư mục tạm: {temp_extract_dir}")
                except Exception as e:
                    print(f"⚠️ Không thể dọn dẹp thư mục tạm: {e}")
        
        return uploaded_files
    
    def is_zip_file(self, file_path: str) -> bool:
        """
        Kiểm tra xem file có phải là zip không
        
        Args:
            file_path: Đường dẫn file
            
        Returns:
            True nếu là zip file
        """
        try:
            # Kiểm tra extension
            if not file_path.lower().endswith('.zip'):
                return False
            
            # Kiểm tra magic number
            with open(file_path, 'rb') as f:
                return f.read(4) == b'\x50\x4b\x03\x04'
        except:
            return False

    async def list_files(self, task_id: str, prefix: str = None) -> List[Dict[str, Any]]:
        """
        Liệt kê các file của một task trên S3
        
        Args:
            task_id: ID của task
            prefix: Prefix để filter files
            
        Returns:
            List các file info
        """
        try:
            base_prefix = f"crawl_results/{task_id}/"
            if prefix:
                base_prefix += prefix
            
            loop = asyncio.get_event_loop()
            
            def list_s3_objects():
                response = self.s3_client.list_objects_v2(
                    Bucket=self.bucket_name,
                    Prefix=base_prefix
                )
                return response.get('Contents', [])
            
            objects = await loop.run_in_executor(None, list_s3_objects)
            
            files = []
            for obj in objects:
                files.append({
                    'key': obj['Key'],
                    'size': obj['Size'],
                    'last_modified': obj['LastModified'].isoformat(),
                    'url': f"https://{self.bucket_name}.s3.{self.aws_region}.amazonaws.com/{obj['Key']}"
                })
            
            return files
            
        except Exception as e:
            print(f"❌ Lỗi khi list files: {str(e)}")
            return []

    async def delete_file(self, s3_key: str) -> bool:
        """
        Xóa file trên S3
        
        Args:
            s3_key: S3 key của file
            
        Returns:
            True nếu xóa thành công
        """
        try:
            if not self._check_s3_available():
                print(f"❌ S3 client not available. Cannot delete file: {s3_key}")
                return False
                
            loop = asyncio.get_event_loop()
            
            def delete_s3_object():
                self.s3_client.delete_object(
                    Bucket=self.bucket_name,
                    Key=s3_key
                )
            
            await loop.run_in_executor(None, delete_s3_object)
            print(f"✅ Đã xóa file: {s3_key}")
            return True
            
        except Exception as e:
            print(f"❌ Lỗi khi xóa file {s3_key}: {str(e)}")
            return False

    async def generate_presigned_url(self, s3_key: str, content_type: str, expiration: int = 3600) -> Optional[Dict[str, Any]]:
        """
        Tạo presigned URL để upload file trực tiếp từ frontend
        
        Args:
            s3_key: S3 key cho file
            content_type: Content type của file
            expiration: Thời gian hết hạn (seconds)
            
        Returns:
            Dict chứa presigned URL và fields hoặc None nếu lỗi
        """
        try:
            if not self._check_s3_available():
                print(f"❌ S3 client not available. Cannot generate presigned URL")
                return None
            
            loop = asyncio.get_event_loop()
            
            def generate_url():
                return self.s3_client.generate_presigned_post(
                    Bucket=self.bucket_name,
                    Key=s3_key,
                    Fields={
                        'Content-Type': content_type
                    },
                    Conditions=[
                        {'Content-Type': content_type},
                        ['content-length-range', 1, 100 * 1024 * 1024]  # Max 100MB
                    ],
                    ExpiresIn=expiration
                )
            
            presigned_data = await loop.run_in_executor(None, generate_url)
            
            return {
                'presigned_url': presigned_data['url'],
                'fields': presigned_data['fields']
            }
            
        except Exception as e:
            print(f"❌ Lỗi khi tạo presigned URL: {str(e)}")
            return None

    def fix_s3_url_encoding(self, broken_url: str) -> str:
        """
        Khắc phục URL S3 bị lỗi do encoding của ký tự tiếng Việt
        
        Args:
            broken_url: URL S3 bị lỗi
            
        Returns:
            URL S3 đã được sửa chữa
        """
        try:
            from urllib.parse import unquote, quote
            import re
            
            # Parse URL để tách các thành phần
            # URL format: https://bucket.s3.region.amazonaws.com/path/to/file
            url_pattern = r'https://([^/]+)\.s3\.([^/]+)\.amazonaws\.com/(.+)'
            match = re.match(url_pattern, broken_url)
            
            if not match:
                print(f"⚠️ URL không đúng định dạng S3: {broken_url}")
                return broken_url
            
            bucket_name = match.group(1)
            region = match.group(2)
            s3_path = match.group(3)
            
            # Decode URL encoding trong path
            decoded_path = unquote(s3_path)
            print(f"📄 Decoded path: {decoded_path}")
            
            # Tách các thành phần path
            path_parts = decoded_path.split('/')
            
            # Sanitize từng phần của path
            sanitized_parts = []
            for part in path_parts:
                if part:  # Bỏ qua empty parts
                    sanitized_part = self._sanitize_path_component(part)
                    sanitized_parts.append(sanitized_part)
            
            # Tạo lại S3 key đã được sanitize
            new_s3_key = '/'.join(sanitized_parts)
            
            # Tạo URL mới
            fixed_url = f"https://{bucket_name}.s3.{region}.amazonaws.com/{new_s3_key}"
            
            print(f"🔧 URL gốc: {broken_url}")
            print(f"✅ URL đã sửa: {fixed_url}")
            
            return fixed_url
            
        except Exception as e:
            print(f"❌ Lỗi khi fix URL: {str(e)}")
            return broken_url

    def create_proper_s3_url(self, task_id: str, original_subfolder: str, original_filename: str) -> str:
        """
        Tạo URL S3 đúng với tên folder và file gốc
        
        Args:
            task_id: ID của task
            original_subfolder: Tên subfolder gốc (có thể chứa tiếng Việt)
            original_filename: Tên file gốc (có thể chứa tiếng Việt)
            
        Returns:
            URL S3 đã được tạo đúng cách
        """
        try:
            # Tạo S3 key với sanitization
            s3_key = self._get_s3_key(task_id, original_filename, original_subfolder)
            
            # Tạo URL
            s3_url = f"https://{self.bucket_name}.s3.{self.aws_region}.amazonaws.com/{s3_key}"
            
            print(f"📁 Original subfolder: {original_subfolder}")
            print(f"📄 Original filename: {original_filename}")
            print(f"🔑 Sanitized S3 key: {s3_key}")
            print(f"🌐 New S3 URL: {s3_url}")
            
            return s3_url
            
        except Exception as e:
            print(f"❌ Lỗi khi tạo URL S3: {str(e)}")
            return None


# Global instance
s3_uploader = S3Uploader()


async def handle_download_s3(download, task_id: str) -> Optional[Dict[str, Any]]:
    """
    Xử lý sự kiện download từ Playwright và upload lên S3
    Nếu là file zip, sẽ giải nén và upload từng file
    
    Args:
        download: Playwright download object
        task_id: ID của task
        
    Returns:
        Dict chứa thông tin file download và upload
    """
    try:
        suggested_filename = download.suggested_filename
        
        # Tạo folder downloads/{task_id} nếu chưa có
        save_dir = os.path.join("downloads", task_id)
        os.makedirs(save_dir, exist_ok=True)

        local_path = os.path.join(save_dir, suggested_filename)
        await download.save_as(local_path)
        print(f"📥 Downloaded file: {suggested_filename} -> {local_path}")

        # Kiểm tra xem có phải file zip không
        if s3_uploader.is_zip_file(local_path):
            print(f"📦 Detected zip file: {suggested_filename}")
            
            # Giải nén và upload từng file
            extracted_files = await s3_uploader.extract_and_upload_zip(local_path, task_id)
            
            if extracted_files:
                # Trả về thông tin về các file đã extract và upload
                return {
                    "type": "zip_extracted",
                    "local_path": local_path,
                    "zip_filename": suggested_filename,
                    "extracted_files": extracted_files,
                    "total_extracted": len(extracted_files),
                    "success_message": f"Đã giải nén và upload {len(extracted_files)} files từ {suggested_filename}"
                }
            else:
                # Nếu giải nén thất bại, upload file zip nguyên bản
                print(f"⚠️ Giải nén thất bại, upload file zip nguyên bản: {suggested_filename}")
                upload_result = await s3_uploader.upload_file(local_path, task_id)
                
                if upload_result:
                    return {
                        "type": "zip_original",
                        "local_path": local_path,
                        "filename": upload_result["filename"],
                        "s3_url": upload_result["s3_url"],
                        "s3_bucket": upload_result["s3_bucket"],
                        "s3_key": upload_result["s3_key"],
                        "content_type": upload_result["content_type"],
                        "file_size": upload_result["file_size"],
                        "upload_time": upload_result["upload_time"],
                        "extraction_failed": True
                    }
        else:
            # File thường, upload trực tiếp
            upload_result = await s3_uploader.upload_file(local_path, task_id)
            
            if upload_result:
                return {
                    "type": "regular_file",
                    "local_path": local_path,
                    "filename": upload_result["filename"],
                    "s3_url": upload_result["s3_url"],
                    "s3_bucket": upload_result["s3_bucket"],
                    "s3_key": upload_result["s3_key"],
                    "content_type": upload_result["content_type"],
                    "file_size": upload_result["file_size"],
                    "upload_time": upload_result["upload_time"]
                }
        
        # Fallback - nếu upload thất bại
        return {
            "type": "upload_failed",
            "local_path": local_path,
            "filename": suggested_filename,
            "s3_url": None,
            "upload_error": "Failed to upload to S3"
        }

    except Exception as e:
        print(f"❌ Error handling download & upload: {str(e)}")
        return {
            "type": "error",
            "filename": download.suggested_filename,
            "error": str(e)
        } 