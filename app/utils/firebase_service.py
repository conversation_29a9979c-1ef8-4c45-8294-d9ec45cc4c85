"""
Firebase Realtime Database service for API history tracking
"""

import json
import os
import asyncio
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List
from functools import wraps
import firebase_admin
from firebase_admin import credentials, db
from ..core.config import settings

class FirebaseService:
    """
    Service để quản lý Firebase Realtime Database cho việc lưu lịch sử API calls
    """
    
    def __init__(self):
        self.app = None
        self.db_ref = None
        self._initialized = False
        
    def initialize(self):
        """
        Khởi tạo Firebase Admin SDK
        """
        if self._initialized:
            return
            
        try:
            # Kiểm tra xem có Firebase credentials không
            firebase_cred_path = getattr(settings, 'FIREBASE_CREDENTIALS_PATH', None)
            firebase_db_url = getattr(settings, 'FIREBASE_DATABASE_URL', None)
            
            if not firebase_cred_path or not firebase_db_url:
                print("⚠️ Firebase credentials not configured. API history logging disabled.")
                return
                
            if not os.path.exists(firebase_cred_path):
                print(f"⚠️ Firebase credentials file not found: {firebase_cred_path}")
                return
                
            # Khởi tạo Firebase Admin SDK
            cred = credentials.Certificate(firebase_cred_path)
            
            # Kiểm tra xem app đã được khởi tạo chưa
            try:
                firebase_admin.get_app()
                print("✅ Firebase app already initialized")
            except ValueError:
                # App chưa được khởi tạo
                self.app = firebase_admin.initialize_app(cred, {
                    'databaseURL': firebase_db_url
                })
                print("✅ Firebase app initialized successfully")
            
            # Tạo reference đến database
            self.db_ref = db.reference()
            self._initialized = True
            
        except Exception as e:
            print(f"❌ Error initializing Firebase: {str(e)}")
            self._initialized = False
    
    def is_available(self) -> bool:
        """
        Kiểm tra xem Firebase service có sẵn sàng không
        """
        return self._initialized and self.db_ref is not None
    
    async def log_api_call(
        self,
        endpoint: str,
        method: str,
        user_id: Optional[str] = None,
        user_ip: Optional[str] = None,
        request_data: Optional[Dict[str, Any]] = None,
        response_data: Optional[Dict[str, Any]] = None,
        status_code: int = 200,
        execution_time: Optional[float] = None,
        error: Optional[str] = None
    ) -> bool:
        """
        Lưu lịch sử API call vào Firebase Realtime Database
        
        Args:
            endpoint: API endpoint được gọi
            method: HTTP method (GET, POST, etc.)
            user_id: ID của user (nếu có)
            user_ip: IP address của user
            request_data: Dữ liệu request
            response_data: Dữ liệu response
            status_code: HTTP status code
            execution_time: Thời gian thực thi (seconds)
            error: Thông tin lỗi (nếu có)
            
        Returns:
            True nếu lưu thành công
        """
        if not self.is_available():
            return False
            
        try:
            # Tạo timestamp
            timestamp = datetime.now(timezone.utc).isoformat()
            
            # Tạo unique ID cho log entry
            log_id = f"{int(datetime.now().timestamp() * 1000)}"
            
            # Chuẩn bị dữ liệu log
            log_data = {
                'timestamp': timestamp,
                'endpoint': endpoint,
                'method': method,
                'user_id': user_id,
                'user_ip': user_ip,
                'status_code': status_code,
                'execution_time': execution_time,
                'error': error,
                'request_data': self._sanitize_data(request_data),
                'response_data': self._sanitize_data(response_data)
            }
            
            # Lưu vào Firebase Realtime Database
            # Structure: /api_history/{date}/{log_id}
            date_key = datetime.now().strftime('%Y-%m-%d')
            
            # Lưu vào path chính
            await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.db_ref.child('api_history').child(date_key).child(log_id).set(log_data)
            )
            
            # Lưu vào user history nếu có user_id
            if user_id:
                await asyncio.get_event_loop().run_in_executor(
                    None,
                    lambda: self.db_ref.child('user_history').child(user_id).child(log_id).set({
                        'timestamp': timestamp,
                        'endpoint': endpoint,
                        'method': method,
                        'status_code': status_code,
                        'execution_time': execution_time
                    })
                )
            
            return True
            
        except Exception as e:
            print(f"❌ Error logging API call to Firebase: {str(e)}")
            return False
    
    async def get_user_history(
        self,
        user_id: str,
        limit: int = 100,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Lấy lịch sử API calls của một user
        
        Args:
            user_id: ID của user
            limit: Số lượng records tối đa
            start_date: Ngày bắt đầu (YYYY-MM-DD)
            end_date: Ngày kết thúc (YYYY-MM-DD)
            
        Returns:
            List các API call history
        """
        if not self.is_available():
            return []
            
        try:
            # Lấy dữ liệu từ Firebase
            user_ref = self.db_ref.child('user_history').child(user_id)
            
            # Thực hiện query trong thread pool để tránh blocking
            data = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: user_ref.order_by_child('timestamp').limit_to_last(limit).get()
            )
            
            if not data:
                return []
            
            # Chuyển đổi dữ liệu
            history = []
            for log_id, log_data in data.items():
                if isinstance(log_data, dict):
                    log_data['log_id'] = log_id
                    history.append(log_data)
            
            # Sắp xếp theo timestamp (mới nhất trước)
            history.sort(key=lambda x: x.get('timestamp', ''), reverse=True)
            
            return history
            
        except Exception as e:
            print(f"❌ Error getting user history from Firebase: {str(e)}")
            return []
    
    async def get_api_stats(
        self,
        date: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Lấy thống kê API usage
        
        Args:
            date: Ngày cần thống kê (YYYY-MM-DD), mặc định là hôm nay
            
        Returns:
            Dict chứa thống kê
        """
        if not self.is_available():
            return {}
            
        try:
            if not date:
                date = datetime.now().strftime('%Y-%m-%d')
            
            # Lấy dữ liệu từ Firebase
            data = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.db_ref.child('api_history').child(date).get()
            )
            
            if not data:
                return {
                    'date': date,
                    'total_calls': 0,
                    'endpoints': {},
                    'status_codes': {},
                    'users': {}
                }
            
            # Tính toán thống kê
            stats = {
                'date': date,
                'total_calls': len(data),
                'endpoints': {},
                'status_codes': {},
                'users': {}
            }
            
            for log_data in data.values():
                if not isinstance(log_data, dict):
                    continue
                
                # Thống kê endpoints
                endpoint = log_data.get('endpoint', 'unknown')
                stats['endpoints'][endpoint] = stats['endpoints'].get(endpoint, 0) + 1
                
                # Thống kê status codes
                status_code = str(log_data.get('status_code', 'unknown'))
                stats['status_codes'][status_code] = stats['status_codes'].get(status_code, 0) + 1
                
                # Thống kê users
                user_id = log_data.get('user_id')
                if user_id:
                    stats['users'][user_id] = stats['users'].get(user_id, 0) + 1
            
            return stats
            
        except Exception as e:
            print(f"❌ Error getting API stats from Firebase: {str(e)}")
            return {}
    
    def _sanitize_data(self, data: Any) -> Any:
        """
        Làm sạch dữ liệu trước khi lưu vào Firebase
        Loại bỏ các thông tin nhạy cảm và giới hạn kích thước
        """
        if data is None:
            return None
            
        if isinstance(data, dict):
            sanitized = {}
            for key, value in data.items():
                # Loại bỏ các field nhạy cảm
                if key.lower() in ['password', 'token', 'api_key', 'secret', 'credential']:
                    sanitized[key] = '[REDACTED]'
                else:
                    sanitized[key] = self._sanitize_data(value)
            return sanitized
            
        elif isinstance(data, list):
            return [self._sanitize_data(item) for item in data[:10]]  # Giới hạn 10 items
            
        elif isinstance(data, str):
            # Giới hạn độ dài string
            return data[:1000] if len(data) > 1000 else data
            
        else:
            return data

# Tạo instance global
firebase_service = FirebaseService()
