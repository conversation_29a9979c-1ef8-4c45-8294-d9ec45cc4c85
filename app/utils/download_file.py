import os
import asyncio
from google.oauth2 import service_account
from googleapiclient.discovery import build
from googleapiclient.http import MediaFileUpload

SERVICE_ACCOUNT_FILE = "./data/thinking-volt-458202-g9-83807f8266b3.json"
SCOPES = ["https://www.googleapis.com/auth/drive.file"]
DRIVE_FOLDER_ID = "114k0VY_h4tB0hPBeGzf7KEsIDYp050ZW"

credentials = service_account.Credentials.from_service_account_file(
    SERVICE_ACCOUNT_FILE, scopes=SCOPES
)
drive_service = build("drive", "v3", credentials=credentials)

async def handle_download(download, task_id: str):
    """
    Xử lý sự kiện download từ Playwright:
    1. Lưu file vào thư mục downloads/{task_id}/...
    2. G<PERSON><PERSON> hàm upload_file_to_drive_localpath để upload lên Google Drive.
    3. Tr<PERSON> về đường dẫn local và thông tin file trên Drive (file_id, file_name).
    """
    try:
        suggested_filename = download.suggested_filename
        # Tạo folder downloads/{task_id} nếu chưa có
        save_dir = os.path.join("downloads", task_id)
        os.makedirs(save_dir, exist_ok=True)

        local_path = os.path.join(save_dir, suggested_filename)
        await download.save_as(local_path)
        print(f"Downloaded file: {suggested_filename} -> {local_path}")

        loop = asyncio.get_event_loop()
        uploaded_info = await loop.run_in_executor(
            None,
            upload_file_to_drive_localpath,
            local_path,
            DRIVE_FOLDER_ID,
        )
        print(f"Uploaded to Drive: id={uploaded_info.get('id')}, name={uploaded_info.get('name')}")

        return {
            "local_path": local_path,
            "drive_id": uploaded_info.get("id"),
            "drive_name": uploaded_info.get("name"),
        }

    except Exception as e:
        print(f"Error handling download & upload: {str(e)}")
        return None


def upload_file_to_drive_localpath(local_path: str, drive_folder_id: str = None) -> dict:
    """
    Upload một file từ local lên Google Drive. 
    Trả về dict {'id': file_id, 'name': file_name} nếu thành công, hoặc raise Exception nếu lỗi.
    """
    filename = os.path.basename(local_path)
    file_metadata = {"name": filename}
    if drive_folder_id:
        file_metadata["parents"] = [drive_folder_id]

    media = MediaFileUpload(local_path, resumable=True)
    uploaded = drive_service.files().create(
        body=file_metadata,
        media_body=media,
        fields="id, name"
    ).execute()
    return uploaded