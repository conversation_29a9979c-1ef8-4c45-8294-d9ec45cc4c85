"""
Background task scheduler để check status của tasks
"""

import asyncio
import time
import httpx
from typing import Dict, Set, Optional
from datetime import datetime, timezone
from ..core.crawler import check_status_by_task, crawl_manus_page_content
from .firebase_service import firebase_service

class TaskScheduler:
    """
    Scheduler để tự động check status của tasks mỗi 5 phút
    """

    def __init__(self):
        self.running_tasks: Dict[str, Dict] = {}  # task_id -> task_info
        self.scheduler_running = False
        self.check_interval = 300  # 5 phút = 300 giây

    async def add_task_to_monitor(
        self,
        task_id: str,
        user_id: str,
        profile_name: Optional[str] = None,
        headless: bool = True,
        max_checks: int = 24  # Tối đa 24 lần check (2 giờ)
    ):
        """
        Thêm task vào danh sách monitor

        Args:
            task_id: ID của task
            user_id: ID của user
            profile_name: Chrome profile name
            headless: Chạy headless mode
            max_checks: <PERSON><PERSON> lần check tối đa
        """
        task_info = {
            'task_id': task_id,
            'user_id': user_id,
            'profile_name': profile_name,
            'headless': headless,
            'created_at': datetime.now(timezone.utc).isoformat(),
            'check_count': 0,
            'max_checks': max_checks,
            'last_check': None,
            'status': 'monitoring'
        }

        self.running_tasks[task_id] = task_info

        # Bắt đầu scheduler nếu chưa chạy
        if not self.scheduler_running:
            asyncio.create_task(self._run_scheduler())

        print(f"✅ Added task {task_id} to monitoring queue")

    async def remove_task_from_monitor(self, task_id: str):
        """
        Xóa task khỏi danh sách monitor
        """
        if task_id in self.running_tasks:
            del self.running_tasks[task_id]
            print(f"✅ Removed task {task_id} from monitoring")

    async def _run_scheduler(self):
        """
        Chạy scheduler chính
        """
        self.scheduler_running = True
        print("🚀 Task scheduler started")

        try:
            while self.running_tasks:
                # Check tất cả tasks
                tasks_to_remove = []

                for task_id, task_info in self.running_tasks.items():
                    try:
                        should_remove = await self._check_single_task(task_id, task_info)
                        if should_remove:
                            tasks_to_remove.append(task_id)
                    except Exception as e:
                        print(f"❌ Error checking task {task_id}: {str(e)}")
                        # Nếu lỗi quá nhiều lần, xóa task
                        task_info['check_count'] += 1
                        if task_info['check_count'] >= task_info['max_checks']:
                            tasks_to_remove.append(task_id)

                # Xóa các tasks đã completed hoặc quá số lần check
                for task_id in tasks_to_remove:
                    await self.remove_task_from_monitor(task_id)

                # Nếu không còn task nào, dừng scheduler
                if not self.running_tasks:
                    break

                # Đợi 5 phút trước khi check tiếp
                print(f"⏰ Waiting {self.check_interval} seconds before next check...")
                await asyncio.sleep(self.check_interval)

        except Exception as e:
            print(f"❌ Scheduler error: {str(e)}")
        finally:
            self.scheduler_running = False
            print("🛑 Task scheduler stopped")

    async def _check_single_task(self, task_id: str, task_info: Dict) -> bool:
        """
        Check status của một task

        Returns:
            True nếu task nên được xóa khỏi monitor
        """
        task_info['check_count'] += 1
        task_info['last_check'] = datetime.now(timezone.utc).isoformat()

        print(f"🔍 Checking task {task_id} (attempt {task_info['check_count']}/{task_info['max_checks']})")

        try:
            # Gọi check_status_by_task
            result = await check_status_by_task(
                task_id=task_id,
                profile_name=task_info['profile_name'],
                headless=task_info['headless'],
                websocket_callback=None  # Không cần websocket callback cho background task
            )

            if result["success"]:
                status = result.get('status', 'unknown')
                detection_method = result.get('detection_method', 'none')

                print(f"✅ Task {task_id} status: {status} (method: {detection_method})")

                # Cập nhật status trong Firebase
                if task_info['user_id']:
                    try:
                        await firebase_service.update_user_task_status(
                            user_id=task_info['user_id'],
                            task_id=task_id,
                            status=status,
                            additional_data={
                                'detection_method': detection_method,
                                'checked_at': task_info['last_check'],
                                'check_count': task_info['check_count']
                            }
                        )
                    except Exception as e:
                        print(f"⚠️ Error updating Firebase: {str(e)}")

                # Nếu task completed, gọi crawl-url để lấy dữ liệu
                if status == "completed":
                    print(f"🎉 Task {task_id} completed! Starting crawl-url...")

                    # Cập nhật status thành "crawling"
                    if task_info['user_id']:
                        try:
                            await firebase_service.update_user_task_status(
                                user_id=task_info['user_id'],
                                task_id=task_id,
                                status="crawling",
                                additional_data={
                                    'crawl_started_at': datetime.now(timezone.utc).isoformat(),
                                    'detection_method': detection_method
                                }
                            )
                        except Exception as e:
                            print(f"⚠️ Error updating status to crawling: {str(e)}")

                    # Gọi crawl-url
                    crawl_success = await self._crawl_task_data(task_id, task_info)

                    if crawl_success:
                        print(f"✅ Task {task_id} crawl completed! Removing from monitor.")
                    else:
                        print(f"❌ Task {task_id} crawl failed! Removing from monitor.")

                    return True

            else:
                print(f"⚠️ Task {task_id} check failed: {result.get('error', 'Unknown error')}")

        except Exception as e:
            print(f"❌ Error checking task {task_id}: {str(e)}")

        # Xóa task nếu đã check quá số lần tối đa
        if task_info['check_count'] >= task_info['max_checks']:
            print(f"⏰ Task {task_id} reached max checks ({task_info['max_checks']}). Removing from monitor.")

            # Cập nhật status là timeout trong Firebase
            if task_info['user_id']:
                try:
                    await firebase_service.update_user_task_status(
                        user_id=task_info['user_id'],
                        task_id=task_id,
                        status="timeout",
                        additional_data={
                            'reason': 'max_checks_reached',
                            'final_check_at': task_info['last_check'],
                            'total_checks': task_info['check_count']
                        }
                    )
                except Exception as e:
                    print(f"⚠️ Error updating Firebase timeout: {str(e)}")

            return True

        return False

    async def _crawl_task_data(self, task_id: str, task_info: Dict) -> bool:
        """
        Gọi crawl-url để lấy dữ liệu sau khi task completed

        Args:
            task_id: ID của task
            task_info: Thông tin task

        Returns:
            True nếu crawl thành công
        """
        try:
            # Tạo task URL từ task_id
            task_url = f"https://manus.im/app/{task_id}"

            print(f"🕷️ Starting crawl for task {task_id} at {task_url}")

            # Callback để update status khi upload
            async def upload_callback(message: str):
                if "uploading" in message.lower() or "upload" in message.lower():
                    print(f"📤 Upload detected for task {task_id}: {message}")

                    # Cập nhật status thành "uploading"
                    if task_info['user_id']:
                        try:
                            await firebase_service.update_user_task_status(
                                user_id=task_info['user_id'],
                                task_id=task_id,
                                status="uploading",
                                additional_data={
                                    'upload_started_at': datetime.now(timezone.utc).isoformat(),
                                    'upload_message': message
                                }
                            )
                        except Exception as e:
                            print(f"⚠️ Error updating status to uploading: {str(e)}")

            # Gọi crawl_manus_page_content
            result = await crawl_manus_page_content(
                url=task_url,
                profile_name=task_info['profile_name'],
                use_system_profile=False,
                headless=task_info['headless'],
                websocket_callback=lambda req_id, msg: asyncio.create_task(upload_callback(msg)),
                request_id=f"scheduler_{task_id}"
            )

            if result["success"]:
                print(f"✅ Crawl successful for task {task_id}")

                # Cập nhật status thành "uploaded" và lưu response data
                if task_info['user_id']:
                    try:
                        await firebase_service.update_user_task_status(
                            user_id=task_info['user_id'],
                            task_id=task_id,
                            status="uploaded",
                            additional_data={
                                'crawl_completed_at': datetime.now(timezone.utc).isoformat(),
                                'crawl_data': self._sanitize_crawl_data(result.get('data', {})),
                                'page_title': result.get('data', {}).get('page_title', ''),
                                'total_messages': len(result.get('data', {}).get('chat_messages', [])),
                                'total_tasks': len(result.get('data', {}).get('tasks', []))
                            }
                        )
                    except Exception as e:
                        print(f"⚠️ Error updating final status: {str(e)}")

                return True
            else:
                print(f"❌ Crawl failed for task {task_id}: {result.get('error', 'Unknown error')}")

                # Cập nhật status thành "crawl_failed"
                if task_info['user_id']:
                    try:
                        await firebase_service.update_user_task_status(
                            user_id=task_info['user_id'],
                            task_id=task_id,
                            status="crawl_failed",
                            additional_data={
                                'crawl_failed_at': datetime.now(timezone.utc).isoformat(),
                                'error': result.get('error', 'Unknown error')
                            }
                        )
                    except Exception as e:
                        print(f"⚠️ Error updating failed status: {str(e)}")

                return False

        except Exception as e:
            print(f"❌ Error crawling task {task_id}: {str(e)}")

            # Cập nhật status thành "crawl_error"
            if task_info['user_id']:
                try:
                    await firebase_service.update_user_task_status(
                        user_id=task_info['user_id'],
                        task_id=task_id,
                        status="crawl_error",
                        additional_data={
                            'crawl_error_at': datetime.now(timezone.utc).isoformat(),
                            'error': str(e)
                        }
                    )
                except Exception as e2:
                    print(f"⚠️ Error updating error status: {str(e2)}")

            return False

    def _sanitize_crawl_data(self, data: Dict) -> Dict:
        """
        Làm sạch crawl data trước khi lưu vào Firebase
        """
        if not isinstance(data, dict):
            return {}

        # Giới hạn kích thước data
        sanitized = {}

        # Lưu page_title
        if 'page_title' in data:
            sanitized['page_title'] = str(data['page_title'])[:200]

        # Lưu số lượng messages và tasks
        if 'chat_messages' in data and isinstance(data['chat_messages'], list):
            sanitized['chat_messages_count'] = len(data['chat_messages'])
            # Lưu một vài message đầu tiên (giới hạn)
            sanitized['sample_messages'] = [
                {
                    'type': msg.get('type', ''),
                    'message_preview': str(msg.get('manus_message', ''))[:100] if msg.get('manus_message') else ''
                }
                for msg in data['chat_messages'][:3]  # Chỉ lưu 3 message đầu
            ]

        if 'tasks' in data and isinstance(data['tasks'], list):
            sanitized['tasks_count'] = len(data['tasks'])
            # Lưu một vài task đầu tiên
            sanitized['sample_tasks'] = [
                {
                    'title': str(task.get('title', ''))[:100],
                    'timestamp': task.get('timestamp', '')
                }
                for task in data['tasks'][:3]  # Chỉ lưu 3 task đầu
            ]

        return sanitized

    def get_monitoring_status(self) -> Dict:
        """
        Lấy trạng thái hiện tại của scheduler
        """
        return {
            'scheduler_running': self.scheduler_running,
            'total_tasks': len(self.running_tasks),
            'tasks': {
                task_id: {
                    'user_id': info['user_id'],
                    'check_count': info['check_count'],
                    'max_checks': info['max_checks'],
                    'last_check': info['last_check'],
                    'status': info['status']
                }
                for task_id, info in self.running_tasks.items()
            }
        }

# Global scheduler instance
task_scheduler = TaskScheduler()
