"""
Background task scheduler để check status của tasks
"""

import asyncio
from typing import Dict, Optional
from datetime import datetime, timezone
from ..core.crawler import check_status_by_task, crawl_manus_page_content
from .firebase_service import firebase_service

class TaskScheduler:
    """
    Scheduler để tự động check status của tasks mỗi 5 phút
    """

    def __init__(self):
        self.running_tasks: Dict[str, Dict] = {}  # task_id -> task_info
        self.scheduler_running = False
        self.check_interval = 300  # 5 phút = 300 giây

    async def add_task_to_monitor(
        self,
        task_id: str,
        user_id: str,
        profile_name: Optional[str] = None,
        headless: bool = True,
        max_checks: int = 24  # Tối đa 24 lần check (2 giờ)
    ):
        """
        Thêm task vào danh sách monitor

        Args:
            task_id: ID của task
            user_id: ID của user
            profile_name: Chrome profile name
            headless: Chạy headless mode
            max_checks: <PERSON><PERSON> lần check tối đa
        """
        task_info = {
            'task_id': task_id,
            'user_id': user_id,
            'profile_name': profile_name,
            'headless': headless,
            'created_at': datetime.now(timezone.utc).isoformat(),
            'check_count': 0,
            'max_checks': max_checks,
            'last_check': None,
            'status': 'monitoring'
        }

        self.running_tasks[task_id] = task_info

        # Bắt đầu scheduler nếu chưa chạy
        if not self.scheduler_running:
            asyncio.create_task(self._run_scheduler())

        print(f"✅ Added task {task_id} to monitoring queue")

    async def remove_task_from_monitor(self, task_id: str):
        """
        Xóa task khỏi danh sách monitor
        """
        if task_id in self.running_tasks:
            del self.running_tasks[task_id]
            print(f"✅ Removed task {task_id} from monitoring")

    async def _run_scheduler(self):
        """
        Chạy scheduler chính
        """
        self.scheduler_running = True
        print("🚀 Task scheduler started")

        try:
            while self.running_tasks:
                # Check tất cả tasks
                tasks_to_remove = []

                for task_id, task_info in self.running_tasks.items():
                    try:
                        should_remove = await self._check_single_task(task_id, task_info)
                        if should_remove:
                            tasks_to_remove.append(task_id)
                    except Exception as e:
                        print(f"❌ Error checking task {task_id}: {str(e)}")
                        # Nếu lỗi quá nhiều lần, xóa task
                        task_info['check_count'] += 1
                        if task_info['check_count'] >= task_info['max_checks']:
                            tasks_to_remove.append(task_id)

                # Xóa các tasks đã completed hoặc quá số lần check
                for task_id in tasks_to_remove:
                    await self.remove_task_from_monitor(task_id)

                # Nếu không còn task nào, dừng scheduler
                if not self.running_tasks:
                    break

                # Đợi 5 phút trước khi check tiếp
                print(f"⏰ Waiting {self.check_interval} seconds before next check...")
                await asyncio.sleep(self.check_interval)

        except Exception as e:
            print(f"❌ Scheduler error: {str(e)}")
        finally:
            self.scheduler_running = False
            print("🛑 Task scheduler stopped")

    async def _check_single_task(self, task_id: str, task_info: Dict) -> bool:
        """
        Check status của một task

        Returns:
            True nếu task nên được xóa khỏi monitor
        """
        task_info['check_count'] += 1
        task_info['last_check'] = datetime.now(timezone.utc).isoformat()

        print(f"🔍 Checking task {task_id} (attempt {task_info['check_count']}/{task_info['max_checks']})")

        try:
            # Gọi check_status_by_task
            result = await check_status_by_task(
                task_id=task_id,
                profile_name=task_info['profile_name'],
                headless=task_info['headless'],
                websocket_callback=None  # Không cần websocket callback cho background task
            )

            if result["success"]:
                status = result.get('status', 'unknown')
                detection_method = result.get('detection_method', 'none')

                print(f"✅ Task {task_id} status: {status} (method: {detection_method})")

                # Cập nhật status trong Firebase
                if task_info['user_id']:
                    try:
                        await firebase_service.update_user_task_status(
                            user_id=task_info['user_id'],
                            task_id=task_id,
                            status=status,
                            additional_data={
                                'detection_method': detection_method,
                                'checked_at': task_info['last_check'],
                                'check_count': task_info['check_count']
                            }
                        )
                    except Exception as e:
                        print(f"⚠️ Error updating Firebase: {str(e)}")

                # Nếu task completed, gọi crawl-url để lấy dữ liệu
                if status == "completed":
                    print(f"🎉 Task {task_id} completed! Starting crawl-url...")

                    # Cập nhật status thành "crawling"
                    if task_info['user_id']:
                        try:
                            await firebase_service.update_user_task_status(
                                user_id=task_info['user_id'],
                                task_id=task_id,
                                status="crawling",
                                additional_data={
                                    'crawl_started_at': datetime.now(timezone.utc).isoformat(),
                                    'detection_method': detection_method
                                }
                            )
                        except Exception as e:
                            print(f"⚠️ Error updating status to crawling: {str(e)}")

                    # Gọi crawl-url
                    crawl_success = await self._crawl_task_data(task_id, task_info)

                    if crawl_success:
                        print(f"✅ Task {task_id} crawl completed! Removing from monitor.")
                    else:
                        print(f"❌ Task {task_id} crawl failed! Removing from monitor.")

                    return True

            else:
                print(f"⚠️ Task {task_id} check failed: {result.get('error', 'Unknown error')}")

        except Exception as e:
            print(f"❌ Error checking task {task_id}: {str(e)}")

        # Xóa task nếu đã check quá số lần tối đa
        if task_info['check_count'] >= task_info['max_checks']:
            print(f"⏰ Task {task_id} reached max checks ({task_info['max_checks']}). Removing from monitor.")

            # Cập nhật status là timeout trong Firebase
            if task_info['user_id']:
                try:
                    await firebase_service.update_user_task_status(
                        user_id=task_info['user_id'],
                        task_id=task_id,
                        status="timeout",
                        additional_data={
                            'reason': 'max_checks_reached',
                            'final_check_at': task_info['last_check'],
                            'total_checks': task_info['check_count']
                        }
                    )
                except Exception as e:
                    print(f"⚠️ Error updating Firebase timeout: {str(e)}")

            return True

        return False

    async def _crawl_task_data(self, task_id: str, task_info: Dict) -> bool:
        """
        Gọi crawl-url để lấy dữ liệu sau khi task completed

        Args:
            task_id: ID của task
            task_info: Thông tin task

        Returns:
            True nếu crawl thành công
        """
        try:
            # Tạo task URL từ task_id
            task_url = f"https://manus.im/app/{task_id}"

            print(f"🕷️ Starting crawl for task {task_id} at {task_url}")

            # Callback để update status khi upload
            async def upload_callback(message: str):
                # Detect upload activities
                upload_keywords = [
                    "uploading", "upload", "đang tải", "batch download",
                    "file uploaded", "zip processed", "starting download"
                ]

                if any(keyword in message.lower() for keyword in upload_keywords):
                    print(f"📤 Upload activity detected for task {task_id}: {message}")

                    # Cập nhật status thành "uploading" nếu chưa phải uploading
                    if task_info['user_id']:
                        try:
                            # Lấy status hiện tại từ Firebase
                            current_data = await asyncio.get_event_loop().run_in_executor(
                                None,
                                lambda: firebase_service.db_ref.child('histories').child(task_info['user_id']).child(task_id).get()
                            )

                            current_status = current_data.get('status', 'unknown') if current_data else 'unknown'

                            # Chỉ update nếu status hiện tại không phải uploading hoặc uploaded
                            if current_status not in ['uploading', 'uploaded']:
                                await firebase_service.update_user_task_status(
                                    user_id=task_info['user_id'],
                                    task_id=task_id,
                                    status="uploading",
                                    additional_data={
                                        'upload_started_at': datetime.now(timezone.utc).isoformat(),
                                        'upload_message': message,
                                        'previous_status': current_status
                                    }
                                )
                                print(f"✅ Updated status to uploading for task {task_id}")
                        except Exception as e:
                            print(f"⚠️ Error updating status to uploading: {str(e)}")

            # Gọi crawl_manus_page_content
            result = await crawl_manus_page_content(
                url=task_url,
                profile_name=task_info['profile_name'],
                use_system_profile=False,
                headless=task_info['headless'],
                websocket_callback=lambda _req_id, msg: asyncio.create_task(upload_callback(msg)),
                request_id=f"scheduler_{task_id}"
            )

            if result["success"]:
                print(f"✅ Crawl successful for task {task_id}")

                # Cập nhật status thành "uploaded" và lưu response data
                if task_info['user_id']:
                    try:
                        # Lưu full crawl data vào Firebase
                        crawl_data = result.get('data', {})

                        await firebase_service.update_user_task_status(
                            user_id=task_info['user_id'],
                            task_id=task_id,
                            status="uploaded",
                            additional_data={
                                'crawl_completed_at': datetime.now(timezone.utc).isoformat(),
                                'crawl_response': {
                                    'success': result.get('success', False),
                                    'data': self._sanitize_crawl_data(crawl_data)
                                },
                                'page_title': crawl_data.get('page_title', ''),
                                'current_task_title': crawl_data.get('current_task_title', ''),
                                'total_messages': len(crawl_data.get('chat_messages', [])),
                                'total_tasks': len(crawl_data.get('tasks', [])),
                                'has_files': self._check_has_files(crawl_data)
                            }
                        )
                    except Exception as e:
                        print(f"⚠️ Error updating final status: {str(e)}")

                return True
            else:
                print(f"❌ Crawl failed for task {task_id}: {result.get('error', 'Unknown error')}")

                # Cập nhật status thành "crawl_failed"
                if task_info['user_id']:
                    try:
                        await firebase_service.update_user_task_status(
                            user_id=task_info['user_id'],
                            task_id=task_id,
                            status="crawl_failed",
                            additional_data={
                                'crawl_failed_at': datetime.now(timezone.utc).isoformat(),
                                'error': result.get('error', 'Unknown error')
                            }
                        )
                    except Exception as e:
                        print(f"⚠️ Error updating failed status: {str(e)}")

                return False

        except Exception as e:
            print(f"❌ Error crawling task {task_id}: {str(e)}")

            # Cập nhật status thành "crawl_error"
            if task_info['user_id']:
                try:
                    await firebase_service.update_user_task_status(
                        user_id=task_info['user_id'],
                        task_id=task_id,
                        status="crawl_error",
                        additional_data={
                            'crawl_error_at': datetime.now(timezone.utc).isoformat(),
                            'error': str(e)
                        }
                    )
                except Exception as e2:
                    print(f"⚠️ Error updating error status: {str(e2)}")

            return False

    def _sanitize_crawl_data(self, data: Dict) -> Dict:
        """
        Làm sạch crawl data trước khi lưu vào Firebase
        Lưu đầy đủ dữ liệu nhưng có giới hạn kích thước
        """
        if not isinstance(data, dict):
            return {}

        sanitized = {}

        # Lưu các field chính
        for key in ['page_title', 'current_task_title']:
            if key in data:
                sanitized[key] = str(data[key])[:500]  # Giới hạn 500 ký tự

        # Lưu chat_messages đầy đủ
        if 'chat_messages' in data and isinstance(data['chat_messages'], list):
            sanitized['chat_messages'] = []
            for msg in data['chat_messages']:
                if isinstance(msg, dict):
                    sanitized_msg = {}

                    # Lưu các field cơ bản
                    for field in ['event_id', 'type', 'timestamp', 'message_subtype']:
                        if field in msg:
                            sanitized_msg[field] = msg[field]

                    # Lưu messages (giới hạn kích thước)
                    if 'user_message' in msg:
                        sanitized_msg['user_message'] = str(msg['user_message'])[:1000]
                    if 'manus_message' in msg:
                        sanitized_msg['manus_message'] = str(msg['manus_message'])[:2000]

                    # Lưu files nếu có
                    if 'cloudinary_files' in msg and isinstance(msg['cloudinary_files'], list):
                        sanitized_msg['cloudinary_files'] = msg['cloudinary_files'][:10]  # Tối đa 10 files

                    sanitized['chat_messages'].append(sanitized_msg)

        # Lưu tasks đầy đủ
        if 'tasks' in data and isinstance(data['tasks'], list):
            sanitized['tasks'] = []
            for task in data['tasks']:
                if isinstance(task, dict):
                    sanitized_task = {}

                    # Lưu các field của task
                    for field in ['icon_src', 'title', 'title_text', 'timestamp', 'preview']:
                        if field in task:
                            if field in ['title', 'title_text', 'preview']:
                                sanitized_task[field] = str(task[field])[:500]  # Giới hạn text
                            else:
                                sanitized_task[field] = task[field]

                    sanitized['tasks'].append(sanitized_task)

        # Lưu footer_user nếu có
        if 'footer_user' in data and isinstance(data['footer_user'], dict):
            sanitized['footer_user'] = {
                'avatar_src': data['footer_user'].get('avatar_src', ''),
                'name': str(data['footer_user'].get('name', ''))[:100]
            }

        # Lưu profile_status nếu có
        if 'profile_status' in data and isinstance(data['profile_status'], dict):
            sanitized['profile_status'] = data['profile_status']

        # Lưu downloaded_files (QUAN TRỌNG)
        if 'downloaded_files' in data and isinstance(data['downloaded_files'], list):
            sanitized['downloaded_files'] = []
            for file_info in data['downloaded_files']:
                if isinstance(file_info, dict):
                    sanitized_file = {}

                    # Lưu tất cả fields của file
                    for field in [
                        'filename', 's3_url', 's3_bucket', 's3_key', 'content_type',
                        'file_size', 'upload_time', 'subfolder', 'task_id',
                        'extracted_from_zip', 'relative_path_in_zip', 'uploaded_at',
                        'type', 'zip_filename', 'total_extracted', 'local_path'
                    ]:
                        if field in file_info:
                            sanitized_file[field] = file_info[field]

                    # Lưu extracted_files nếu có (cho zip_summary)
                    if 'extracted_files' in file_info and isinstance(file_info['extracted_files'], list):
                        sanitized_file['extracted_files'] = file_info['extracted_files'][:20]  # Tối đa 20 files

                    sanitized['downloaded_files'].append(sanitized_file)

        # Lưu task_id và total_files nếu có
        if 'task_id' in data:
            sanitized['task_id'] = data['task_id']
        if 'total_files' in data:
            sanitized['total_files'] = data['total_files']

        return sanitized

    def _check_has_files(self, data: Dict) -> bool:
        """
        Kiểm tra xem có file nào được upload không
        """
        if not isinstance(data, dict):
            return False

        # Kiểm tra cloudinary_files trong chat_messages
        chat_messages = data.get('chat_messages', [])
        for msg in chat_messages:
            if isinstance(msg, dict) and 'cloudinary_files' in msg:
                files = msg.get('cloudinary_files', [])
                if isinstance(files, list) and len(files) > 0:
                    return True

        # Kiểm tra downloaded_files
        downloaded_files = data.get('downloaded_files', [])
        if isinstance(downloaded_files, list) and len(downloaded_files) > 0:
            return True

        return False

    def get_monitoring_status(self) -> Dict:
        """
        Lấy trạng thái hiện tại của scheduler
        """
        return {
            'scheduler_running': self.scheduler_running,
            'total_tasks': len(self.running_tasks),
            'tasks': {
                task_id: {
                    'user_id': info['user_id'],
                    'check_count': info['check_count'],
                    'max_checks': info['max_checks'],
                    'last_check': info['last_check'],
                    'status': info['status']
                }
                for task_id, info in self.running_tasks.items()
            }
        }

# Global scheduler instance
task_scheduler = TaskScheduler()
