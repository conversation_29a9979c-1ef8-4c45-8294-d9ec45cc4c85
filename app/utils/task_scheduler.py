"""
Background task scheduler để check status của tasks
"""

import asyncio
import time
from typing import Dict, Set, Optional
from datetime import datetime, timezone
from ..core.crawler import check_status_by_task
from .firebase_service import firebase_service

class TaskScheduler:
    """
    Scheduler để tự động check status của tasks mỗi 5 phút
    """
    
    def __init__(self):
        self.running_tasks: Dict[str, Dict] = {}  # task_id -> task_info
        self.scheduler_running = False
        self.check_interval = 300  # 5 phút = 300 giây
        
    async def add_task_to_monitor(
        self,
        task_id: str,
        user_id: str,
        profile_name: Optional[str] = None,
        headless: bool = True,
        max_checks: int = 24  # Tối đa 24 lần check (2 giờ)
    ):
        """
        Thêm task vào danh sách monitor
        
        Args:
            task_id: ID của task
            user_id: ID của user
            profile_name: Chrome profile name
            headless: Chạy headless mode
            max_checks: <PERSON><PERSON> lần check tối đa
        """
        task_info = {
            'task_id': task_id,
            'user_id': user_id,
            'profile_name': profile_name,
            'headless': headless,
            'created_at': datetime.now(timezone.utc).isoformat(),
            'check_count': 0,
            'max_checks': max_checks,
            'last_check': None,
            'status': 'monitoring'
        }
        
        self.running_tasks[task_id] = task_info
        
        # Bắt đầu scheduler nếu chưa chạy
        if not self.scheduler_running:
            asyncio.create_task(self._run_scheduler())
        
        print(f"✅ Added task {task_id} to monitoring queue")
        
    async def remove_task_from_monitor(self, task_id: str):
        """
        Xóa task khỏi danh sách monitor
        """
        if task_id in self.running_tasks:
            del self.running_tasks[task_id]
            print(f"✅ Removed task {task_id} from monitoring")
            
    async def _run_scheduler(self):
        """
        Chạy scheduler chính
        """
        self.scheduler_running = True
        print("🚀 Task scheduler started")
        
        try:
            while self.running_tasks:
                # Check tất cả tasks
                tasks_to_remove = []
                
                for task_id, task_info in self.running_tasks.items():
                    try:
                        should_remove = await self._check_single_task(task_id, task_info)
                        if should_remove:
                            tasks_to_remove.append(task_id)
                    except Exception as e:
                        print(f"❌ Error checking task {task_id}: {str(e)}")
                        # Nếu lỗi quá nhiều lần, xóa task
                        task_info['check_count'] += 1
                        if task_info['check_count'] >= task_info['max_checks']:
                            tasks_to_remove.append(task_id)
                
                # Xóa các tasks đã completed hoặc quá số lần check
                for task_id in tasks_to_remove:
                    await self.remove_task_from_monitor(task_id)
                
                # Nếu không còn task nào, dừng scheduler
                if not self.running_tasks:
                    break
                    
                # Đợi 5 phút trước khi check tiếp
                print(f"⏰ Waiting {self.check_interval} seconds before next check...")
                await asyncio.sleep(self.check_interval)
                
        except Exception as e:
            print(f"❌ Scheduler error: {str(e)}")
        finally:
            self.scheduler_running = False
            print("🛑 Task scheduler stopped")
    
    async def _check_single_task(self, task_id: str, task_info: Dict) -> bool:
        """
        Check status của một task
        
        Returns:
            True nếu task nên được xóa khỏi monitor
        """
        task_info['check_count'] += 1
        task_info['last_check'] = datetime.now(timezone.utc).isoformat()
        
        print(f"🔍 Checking task {task_id} (attempt {task_info['check_count']}/{task_info['max_checks']})")
        
        try:
            # Gọi check_status_by_task
            result = await check_status_by_task(
                task_id=task_id,
                profile_name=task_info['profile_name'],
                headless=task_info['headless'],
                websocket_callback=None  # Không cần websocket callback cho background task
            )
            
            if result["success"]:
                status = result.get('status', 'unknown')
                detection_method = result.get('detection_method', 'none')
                
                print(f"✅ Task {task_id} status: {status} (method: {detection_method})")
                
                # Cập nhật status trong Firebase
                if task_info['user_id']:
                    try:
                        await firebase_service.update_user_task_status(
                            user_id=task_info['user_id'],
                            task_id=task_id,
                            status=status,
                            additional_data={
                                'detection_method': detection_method,
                                'checked_at': task_info['last_check'],
                                'check_count': task_info['check_count']
                            }
                        )
                    except Exception as e:
                        print(f"⚠️ Error updating Firebase: {str(e)}")
                
                # Nếu task completed, xóa khỏi monitor
                if status == "completed":
                    print(f"🎉 Task {task_id} completed! Removing from monitor.")
                    return True
                    
            else:
                print(f"⚠️ Task {task_id} check failed: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            print(f"❌ Error checking task {task_id}: {str(e)}")
        
        # Xóa task nếu đã check quá số lần tối đa
        if task_info['check_count'] >= task_info['max_checks']:
            print(f"⏰ Task {task_id} reached max checks ({task_info['max_checks']}). Removing from monitor.")
            
            # Cập nhật status là timeout trong Firebase
            if task_info['user_id']:
                try:
                    await firebase_service.update_user_task_status(
                        user_id=task_info['user_id'],
                        task_id=task_id,
                        status="timeout",
                        additional_data={
                            'reason': 'max_checks_reached',
                            'final_check_at': task_info['last_check'],
                            'total_checks': task_info['check_count']
                        }
                    )
                except Exception as e:
                    print(f"⚠️ Error updating Firebase timeout: {str(e)}")
            
            return True
            
        return False
    
    def get_monitoring_status(self) -> Dict:
        """
        Lấy trạng thái hiện tại của scheduler
        """
        return {
            'scheduler_running': self.scheduler_running,
            'total_tasks': len(self.running_tasks),
            'tasks': {
                task_id: {
                    'user_id': info['user_id'],
                    'check_count': info['check_count'],
                    'max_checks': info['max_checks'],
                    'last_check': info['last_check'],
                    'status': info['status']
                }
                for task_id, info in self.running_tasks.items()
            }
        }

# Global scheduler instance
task_scheduler = TaskScheduler()
