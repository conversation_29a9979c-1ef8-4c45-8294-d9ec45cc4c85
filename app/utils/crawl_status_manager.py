import asyncio
import json
import os
from datetime import datetime
from typing import Dict, Any, List, Optional
from enum import Enum

class CrawlStatus(Enum):
    """Trạng thái của crawl task"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    UPLOADING = "uploading"
    COMPLETED = "completed"
    ERROR = "error"

class CrawlStatusManager:
    """
    Manager để theo dõi trạng thái crawl và danh sách file uploads
    """
    
    def __init__(self, storage_dir: str = "data/crawl_status"):
        self.storage_dir = storage_dir
        os.makedirs(storage_dir, exist_ok=True)
        
    def _get_status_file_path(self, task_id: str) -> str:
        """Lấy đường dẫn file status cho task"""
        return os.path.join(self.storage_dir, f"{task_id}.json")
    
    async def create_task(self, task_id: str, initial_data: Dict[str, Any] = None) -> bool:
        """
        Tạo task mới với trạng thái pending
        
        Args:
            task_id: ID của task
            initial_data: Dữ liệu khởi tạo
            
        Returns:
            True nếu tạo thành công
        """
        try:
            status_data = {
                "task_id": task_id,
                "status": CrawlStatus.PENDING.value,
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat(),
                "uploaded_files": [],
                "error": None,
                "data": initial_data or {}
            }
            
            status_file = self._get_status_file_path(task_id)
            with open(status_file, 'w', encoding='utf-8') as f:
                json.dump(status_data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ Created task: {task_id}")
            return True
            
        except Exception as e:
            print(f"❌ Error creating task {task_id}: {str(e)}")
            return False
    
    async def update_status(self, task_id: str, status: CrawlStatus, error: str = None) -> bool:
        """
        Cập nhật trạng thái task
        
        Args:
            task_id: ID của task
            status: Trạng thái mới
            error: Thông tin lỗi (nếu có)
            
        Returns:
            True nếu update thành công
        """
        try:
            status_file = self._get_status_file_path(task_id)
            
            if not os.path.exists(status_file):
                print(f"⚠️ Task {task_id} not found")
                return False
            
            with open(status_file, 'r', encoding='utf-8') as f:
                status_data = json.load(f)
            
            status_data["status"] = status.value
            status_data["updated_at"] = datetime.now().isoformat()
            
            if error:
                status_data["error"] = error
            
            with open(status_file, 'w', encoding='utf-8') as f:
                json.dump(status_data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ Updated task {task_id} status to: {status.value}")
            return True
            
        except Exception as e:
            print(f"❌ Error updating task {task_id}: {str(e)}")
            return False
    
    async def add_uploaded_file(self, task_id: str, file_info: Dict[str, Any]) -> bool:
        """
        Thêm thông tin file đã upload
        
        Args:
            task_id: ID của task
            file_info: Thông tin file upload
            
        Returns:
            True nếu thêm thành công
        """
        try:
            status_file = self._get_status_file_path(task_id)
            
            if not os.path.exists(status_file):
                print(f"⚠️ Task {task_id} not found")
                return False
            
            with open(status_file, 'r', encoding='utf-8') as f:
                status_data = json.load(f)
            
            # Thêm timestamp cho file info
            file_info["uploaded_at"] = datetime.now().isoformat()
            
            status_data["uploaded_files"].append(file_info)
            status_data["updated_at"] = datetime.now().isoformat()
            
            with open(status_file, 'w', encoding='utf-8') as f:
                json.dump(status_data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ Added uploaded file to task {task_id}: {file_info.get('filename')}")
            return True
            
        except Exception as e:
            print(f"❌ Error adding file to task {task_id}: {str(e)}")
            return False
    
    async def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        Lấy thông tin trạng thái task
        
        Args:
            task_id: ID của task
            
        Returns:
            Dict chứa thông tin task hoặc None nếu không tồn tại
        """
        try:
            status_file = self._get_status_file_path(task_id)
            
            if not os.path.exists(status_file):
                return None
            
            with open(status_file, 'r', encoding='utf-8') as f:
                status_data = json.load(f)
            
            return status_data
            
        except Exception as e:
            print(f"❌ Error getting task {task_id}: {str(e)}")
            return None
    
    async def get_uploaded_files(self, task_id: str) -> List[Dict[str, Any]]:
        """
        Lấy danh sách file đã upload của task
        
        Args:
            task_id: ID của task
            
        Returns:
            List các file đã upload
        """
        task_status = await self.get_task_status(task_id)
        if task_status:
            return task_status.get("uploaded_files", [])
        return []
    
    async def cleanup_old_tasks(self, days: int = 7) -> int:
        """
        Xóa các task cũ hơn số ngày specified
        
        Args:
            days: Số ngày
            
        Returns:
            Số task đã xóa
        """
        try:
            from datetime import timedelta
            cutoff_date = datetime.now() - timedelta(days=days)
            deleted_count = 0
            
            for filename in os.listdir(self.storage_dir):
                if filename.endswith('.json'):
                    file_path = os.path.join(self.storage_dir, filename)
                    
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            task_data = json.load(f)
                        
                        created_at = datetime.fromisoformat(task_data.get("created_at", ""))
                        
                        if created_at < cutoff_date:
                            os.remove(file_path)
                            deleted_count += 1
                            print(f"🗑️ Deleted old task: {task_data.get('task_id')}")
                            
                    except Exception as e:
                        print(f"⚠️ Error processing file {filename}: {str(e)}")
                        continue
            
            print(f"✅ Cleaned up {deleted_count} old tasks")
            return deleted_count
            
        except Exception as e:
            print(f"❌ Error during cleanup: {str(e)}")
            return 0

# Global instance
crawl_status_manager = CrawlStatusManager() 