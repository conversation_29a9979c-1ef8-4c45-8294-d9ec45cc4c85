import os
import asyncio
import aiofiles
import aiohttp
import zipfile
import tempfile
import shutil
from typing import Dict, Any, Optional, List
from pathlib import Path

class CloudinaryUploader:
    """
    Class để upload file lên Cloudinary thay thế Google Drive
    """
    
    def __init__(self):
        self.api_url = "https://api.cloudinary.com/v1_1/test-upload-1/auto/upload"
        self.upload_preset = "AutoWork"
        self.api_key = "238276161954221"
    
    async def upload_file(self, file_path: str, task_id: str, subfolder: str = None) -> Optional[Dict[str, Any]]:
        """
        Upload file lên Cloudinary
        
        Args:
            file_path: Đường dẫn file local
            task_id: ID của task để tổ chức file
            subfolder: Subfolder bên trong task folder (cho file từ zip)
            
        Returns:
            Dict chứa thông tin file upload hoặc None nếu lỗi
        """
        try:
            if not os.path.exists(file_path):
                print(f"File không tồn tại: {file_path}")
                return None
            
            filename = os.path.basename(file_path)
            
            # Đọc file content
            async with aiofiles.open(file_path, 'rb') as f:
                file_content = await f.read()
            
            # Tạo folder path
            folder_path = f"crawl_results/{task_id}"
            if subfolder:
                folder_path += f"/{subfolder}"
            
            # Tạo form data
            form_data = aiohttp.FormData()
            form_data.add_field('file', file_content, filename=filename)
            form_data.add_field('upload_preset', self.upload_preset)
            form_data.add_field('api_key', self.api_key)
            form_data.add_field('folder', folder_path)
            
            # Upload file
            async with aiohttp.ClientSession() as session:
                async with session.post(self.api_url, data=form_data) as response:
                    if response.status == 200:
                        result = await response.json()
                        print(f"✅ Upload thành công: {filename} -> {result.get('secure_url')}")
                        
                        return {
                            "filename": filename,
                            "cloudinary_url": result.get("secure_url"),
                            "public_id": result.get("public_id"),
                            "resource_type": result.get("resource_type"),
                            "format": result.get("format"),
                            "bytes": result.get("bytes"),
                            "upload_time": result.get("created_at"),
                            "subfolder": subfolder
                        }
                    else:
                        error_text = await response.text()
                        print(f"❌ Upload thất bại: {response.status} - {error_text}")
                        return None
                        
        except Exception as e:
            print(f"❌ Lỗi upload file {file_path}: {str(e)}")
            return None

    async def extract_and_upload_zip(self, zip_path: str, task_id: str) -> List[Dict[str, Any]]:
        """
        Giải nén file zip và upload từng file lên Cloudinary
        
        Args:
            zip_path: Đường dẫn file zip
            task_id: ID của task
            
        Returns:
            List các Dict chứa thông tin các file đã upload
        """
        uploaded_files = []
        temp_extract_dir = None
        
        try:
            # Tạo thư mục tạm để giải nén
            temp_extract_dir = tempfile.mkdtemp(prefix=f"extract_{task_id}_")
            print(f"📦 Giải nén zip file: {zip_path} -> {temp_extract_dir}")
            
            # Giải nén file zip
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(temp_extract_dir)
            
            # Lấy tên zip file (không có extension) để làm subfolder
            zip_filename = Path(zip_path).stem
            
            # Upload từng file trong thư mục giải nén
            for root, dirs, files in os.walk(temp_extract_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    
                    # Tạo relative path từ extract dir để làm subfolder
                    relative_path = os.path.relpath(root, temp_extract_dir)
                    if relative_path == ".":
                        # File ở root của zip
                        subfolder = zip_filename
                    else:
                        # File ở subfolder của zip
                        subfolder = f"{zip_filename}/{relative_path}"
                    
                    # Upload file
                    upload_result = await self.upload_file(file_path, task_id, subfolder)
                    
                    if upload_result:
                        # Thêm thông tin về original zip
                        upload_result["extracted_from_zip"] = os.path.basename(zip_path)
                        upload_result["relative_path_in_zip"] = os.path.relpath(file_path, temp_extract_dir)
                        uploaded_files.append(upload_result)
                        print(f"  ✅ Uploaded extracted file: {file}")
                    else:
                        print(f"  ❌ Failed to upload extracted file: {file}")
            
            print(f"✅ Hoàn thành giải nén và upload {len(uploaded_files)} files từ zip")
            
        except zipfile.BadZipFile:
            print(f"❌ File không phải zip hợp lệ: {zip_path}")
        except Exception as e:
            print(f"❌ Lỗi khi giải nén và upload zip: {str(e)}")
        finally:
            # Dọn dẹp thư mục tạm
            if temp_extract_dir and os.path.exists(temp_extract_dir):
                try:
                    shutil.rmtree(temp_extract_dir)
                    print(f"🧹 Đã dọn dẹp thư mục tạm: {temp_extract_dir}")
                except Exception as e:
                    print(f"⚠️ Không thể dọn dẹp thư mục tạm: {e}")
        
        return uploaded_files
    
    def is_zip_file(self, file_path: str) -> bool:
        """
        Kiểm tra xem file có phải là zip không
        
        Args:
            file_path: Đường dẫn file
            
        Returns:
            True nếu là zip file
        """
        try:
            # Kiểm tra extension
            if not file_path.lower().endswith('.zip'):
                return False
            
            # Kiểm tra magic number
            with open(file_path, 'rb') as f:
                return f.read(4) == b'\x50\x4b\x03\x04'
        except:
            return False

# Global instance
cloudinary_uploader = CloudinaryUploader()

async def handle_download_cloudinary(download, task_id: str) -> Optional[Dict[str, Any]]:
    """
    Xử lý sự kiện download từ Playwright và upload lên Cloudinary
    Nếu là file zip, sẽ giải nén và upload từng file
    
    Args:
        download: Playwright download object
        task_id: ID của task
        
    Returns:
        Dict chứa thông tin file download và upload
    """
    try:
        suggested_filename = download.suggested_filename
        
        # Tạo folder downloads/{task_id} nếu chưa có
        save_dir = os.path.join("downloads", task_id)
        os.makedirs(save_dir, exist_ok=True)

        local_path = os.path.join(save_dir, suggested_filename)
        await download.save_as(local_path)
        print(f"📥 Downloaded file: {suggested_filename} -> {local_path}")

        # Kiểm tra xem có phải file zip không
        if cloudinary_uploader.is_zip_file(local_path):
            print(f"📦 Detected zip file: {suggested_filename}")
            
            # Giải nén và upload từng file
            extracted_files = await cloudinary_uploader.extract_and_upload_zip(local_path, task_id)
            
            if extracted_files:
                # Trả về thông tin về các file đã extract và upload
                return {
                    "type": "zip_extracted",
                    "local_path": local_path,
                    "zip_filename": suggested_filename,
                    "extracted_files": extracted_files,
                    "total_extracted": len(extracted_files),
                    "success_message": f"Đã giải nén và upload {len(extracted_files)} files từ {suggested_filename}"
                }
            else:
                # Nếu giải nén thất bại, upload file zip nguyên bản
                print(f"⚠️ Giải nén thất bại, upload file zip nguyên bản: {suggested_filename}")
                upload_result = await cloudinary_uploader.upload_file(local_path, task_id)
                
                if upload_result:
                    return {
                        "type": "zip_original",
                        "local_path": local_path,
                        "filename": upload_result["filename"],
                        "cloudinary_url": upload_result["cloudinary_url"],
                        "public_id": upload_result["public_id"],
                        "resource_type": upload_result["resource_type"],
                        "format": upload_result["format"],
                        "bytes": upload_result["bytes"],
                        "upload_time": upload_result["upload_time"],
                        "extraction_failed": True
                    }
        else:
            # File thường, upload trực tiếp
            upload_result = await cloudinary_uploader.upload_file(local_path, task_id)
            
            if upload_result:
                return {
                    "type": "regular_file",
                    "local_path": local_path,
                    "filename": upload_result["filename"],
                    "cloudinary_url": upload_result["cloudinary_url"],
                    "public_id": upload_result["public_id"],
                    "resource_type": upload_result["resource_type"],
                    "format": upload_result["format"],
                    "bytes": upload_result["bytes"],
                    "upload_time": upload_result["upload_time"]
                }
        
        # Fallback - nếu upload thất bại
        return {
            "type": "upload_failed",
            "local_path": local_path,
            "filename": suggested_filename,
            "cloudinary_url": None,
            "upload_error": "Failed to upload to Cloudinary"
        }

    except Exception as e:
        print(f"❌ Error handling download & upload: {str(e)}")
        return {
            "type": "error",
            "filename": download.suggested_filename,
            "error": str(e)
        } 