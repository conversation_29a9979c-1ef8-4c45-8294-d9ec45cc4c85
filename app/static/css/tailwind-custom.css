/* Tailwind CSS custom utilities and configurations */

/* Custom color variables */
:root {
  --primary-color: #4a6cf7;
  --secondary-color: #6941c6;
  --success-color: #10b981;
  --danger-color: #ef4444;
  --warning-color: #f59e0b;
  --info-color: #3b82f6;
}

/* Custom utilities */
.btn-primary {
  @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded;
}

.btn-secondary {
  @apply bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded;
}

.btn-danger {
  @apply bg-red-500 hover:bg-red-600 text-white font-medium py-2 px-4 rounded;
}

.btn-success {
  @apply bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-4 rounded;
}

.card {
  @apply bg-white rounded-lg shadow-md p-6 mb-4;
}

.card-header {
  @apply flex justify-between items-center mb-4 pb-3 border-b;
}

.form-group {
  @apply mb-4;
}

.form-label {
  @apply block mb-2 text-sm font-medium text-gray-700;
}

.form-input {
  @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500;
}

.fade-in {
  animation: fadeIn 0.5s;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.slide-in {
  animation: slideIn 0.3s;
}

@keyframes slideIn {
  from { transform: translateY(-20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
} 