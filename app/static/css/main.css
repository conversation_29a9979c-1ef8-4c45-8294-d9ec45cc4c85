/* Modern Admin UI Theme with Sidebar - Main CSS */
:root {
  /* Color variables */
  --primary-color: #3b82f6;
  --primary-light: #60a5fa;
  --primary-dark: #2563eb;
  --secondary-color: #10b981;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --info-color: #0ea5e9;
  --success-color: #22c55e;
  
  /* Background colors */
  --bg-main: #f3f4f6;
  --bg-card: #ffffff;
  --bg-sidebar: #111827;
  --bg-sidebar-active: rgba(255, 255, 255, 0.08);
  --bg-header: #ffffff;
  --bg-hover: rgba(59, 130, 246, 0.08);
  
  /* Border colors */
  --border-color: #e4e4e7;
  --border-focus: var(--primary-color);
  
  /* Text colors */
  --text-color: #374151;
  --text-muted: #6b7280;
  --text-light: #9ca3af;
  --text-white: #ffffff;
  --text-sidebar: #9ca3af;
  --text-sidebar-active: #ffffff;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Typography */
  --font-sans: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  
  /* Transitions */
  --transition: all 0.2s ease;
  
  /* Layout */
  --header-height: 64px;
  --sidebar-width: 260px;
  --sidebar-collapsed-width: 70px;
  --border-radius: 8px;
  --border-radius-lg: 12px;
  --container-width: 1200px;
  --content-padding: 1.5rem;
}

/* Base styles */
*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  height: 100%;
}

body {
  font-family: var(--font-sans);
  background-color: var(--bg-main);
  color: var(--text-color);
  margin: 0;
  padding: 0;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
}

h1, h2, h3, h4, h5, h6 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-weight: 600;
  line-height: 1.2;
  color: var(--text-color);
}

h1 { font-size: 1.75rem; }
h2 { font-size: 1.5rem; }
h3 { font-size: 1.25rem; }
h4 { font-size: 1rem; }
h5 { font-size: 0.875rem; }
h6 { font-size: 0.75rem; }

p {
  margin-top: 0;
  margin-bottom: 1rem;
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: var(--transition);
}

a:hover {
  color: var(--primary-dark);
  text-decoration: none;
}

img {
  max-width: 100%;
  height: auto;
}

/* Layout */
.app-container {
  display: flex;
  flex-direction: row;
  min-height: 100vh;
  width: 100%;
}

.sidebar {
  width: var(--sidebar-width);
  background-color: var(--bg-sidebar);
  color: var(--text-sidebar);
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  z-index: 100;
  transition: var(--transition);
  display: flex;
  flex-direction: column;
  box-shadow: var(--shadow);
  overflow-y: auto;
}

.sidebar.collapsed {
  width: var(--sidebar-collapsed-width);
}

.sidebar-header {
  height: var(--header-height);
  padding: 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: var(--text-white);
  font-weight: 600;
  font-size: 1.25rem;
  white-space: nowrap;
  overflow: hidden;
}

.sidebar.collapsed .sidebar-logo-text {
  display: none;
}

.sidebar.collapsed .menu-title {
  display: none;
}

.sidebar-toggle {
  background: transparent;
  border: none;
  color: var(--text-sidebar);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  transition: var(--transition);
}

.sidebar-toggle:hover {
  background-color: var(--bg-sidebar-active);
  color: var(--text-sidebar-active);
}

.sidebar-menu {
  padding: 1rem 0;
  flex: 1;
}

.menu-section {
  margin-bottom: 1.5rem;
}

.menu-title {
  padding: 0.5rem 1.25rem;
  font-size: 0.75rem;
  text-transform: uppercase;
  font-weight: 600;
  color: var(--text-light);
  letter-spacing: 0.05em;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.menu-items {
  list-style: none;
  padding: 0;
  margin: 0;
}

.menu-item {
  position: relative;
}

.menu-link {
  display: flex;
  align-items: center;
  padding: 0.75rem 1.25rem;
  color: var(--text-sidebar);
  transition: var(--transition);
  gap: 0.75rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.sidebar.collapsed .menu-link {
  padding: 0.75rem;
  justify-content: center;
}

.menu-icon {
  font-size: 1.25rem;
  min-width: 1.25rem;
  text-align: center;
}

.menu-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.sidebar.collapsed .menu-text {
  display: none;
}

.menu-link:hover, .menu-link.active {
  background-color: var(--bg-sidebar-active);
  color: var(--text-sidebar-active);
}

.menu-link.active {
  border-left: 3px solid var(--primary-color);
}

.sidebar.collapsed .menu-link.active {
  border-left: none;
  border-left-color: transparent;
}

.submenu {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
  background-color: rgba(0, 0, 0, 0.15);
}

.submenu.open {
  max-height: 1000px;
}

.submenu .menu-link {
  padding-left: 3.25rem;
  font-size: 0.9rem;
}

.sidebar.collapsed .submenu {
  display: none;
}

.menu-arrow {
  margin-left: auto;
  transition: transform 0.3s ease;
}

.menu-item.open .menu-arrow {
  transform: rotate(90deg);
}

.sidebar.collapsed .menu-arrow {
  display: none;
}

.main-content {
  flex: 1;
  margin-left: var(--sidebar-width);
  transition: var(--transition);
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.sidebar.collapsed ~ .main-content {
  margin-left: var(--sidebar-collapsed-width);
}

.main-header {
  height: var(--header-height);
  background-color: var(--bg-header);
  border-bottom: 1px solid var(--border-color);
  padding: 0 var(--content-padding);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: 99;
  box-shadow: var(--shadow-sm);
}

.header-title {
  font-size: 1.25rem;
  font-weight: 600;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.content-wrapper {
  padding: var(--content-padding);
  flex: 1;
}

/* Tooltip for collapsed sidebar */
.tooltip {
  position: relative;
}

.tooltip .tooltip-text {
  visibility: hidden;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  text-align: center;
  border-radius: 4px;
  padding: 5px 10px;
  position: absolute;
  z-index: 200;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  margin-left: 12px;
  opacity: 0;
  transition: opacity 0.3s;
  font-size: 12px;
  white-space: nowrap;
  pointer-events: none;
}

.sidebar.collapsed .tooltip:hover .tooltip-text {
  visibility: visible;
  opacity: 1;
}

/* Cards */
.card {
  background-color: var(--bg-card);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  margin-bottom: 1.5rem;
  transition: var(--transition);
}

.card:hover {
  box-shadow: var(--shadow);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.card-body {
  padding: 1.5rem;
}

.card-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 0.5rem;
}

/* Grid system */
.row {
  display: flex;
  flex-wrap: wrap;
  margin-right: -0.75rem;
  margin-left: -0.75rem;
}

.col, .col-2, .col-3, .col-4, .col-6, .col-8, .col-12 {
  position: relative;
  width: 100%;
  padding-right: 0.75rem;
  padding-left: 0.75rem;
}

.col { flex: 1 0 0%; }
.col-2 { flex: 0 0 16.66667%; max-width: 16.66667%; }
.col-3 { flex: 0 0 25%; max-width: 25%; }
.col-4 { flex: 0 0 33.33333%; max-width: 33.33333%; }
.col-6 { flex: 0 0 50%; max-width: 50%; }
.col-8 { flex: 0 0 66.66667%; max-width: 66.66667%; }
.col-12 { flex: 0 0 100%; max-width: 100%; }

/* Forms */
.form-group {
  margin-bottom: 1rem;
}

label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.form-control {
  display: block;
  width: 100%;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: var(--text-color);
  background-color: var(--bg-card);
  background-clip: padding-box;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.form-control:focus {
  border-color: var(--border-focus);
  outline: 0;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.25);
}

textarea.form-control {
  min-height: 100px;
  resize: vertical;
}

.form-check {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.form-check-input {
  width: 1rem;
  height: 1rem;
  margin: 0;
}

.form-actions {
  display: flex;
  gap: 0.75rem;
  margin-top: 1.5rem;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  user-select: none;
  border: 1px solid transparent;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  line-height: 1.5;
  border-radius: var(--border-radius);
  transition: var(--transition);
  cursor: pointer;
  gap: 0.5rem;
}

.btn:hover {
  transform: translateY(-1px);
}

.btn:focus {
  outline: 0;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.25);
}

.btn:disabled {
  opacity: 0.65;
  cursor: not-allowed;
  pointer-events: none;
}

.btn-icon {
  padding: 0.5rem;
  width: 2.5rem;
  height: 2.5rem;
  justify-content: center;
}

.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
}

.btn-lg {
  padding: 1rem 2rem;
  font-size: 1.125rem;
}

.btn-primary {
  color: #fff;
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
}

.btn-secondary {
  color: var(--text-color);
  background-color: var(--bg-card);
  border-color: var(--border-color);
}

.btn-secondary:hover {
  background-color: var(--bg-main);
}

.btn-success {
  color: #fff;
  background-color: var(--success-color);
  border-color: var(--success-color);
}

.btn-danger {
  color: #fff;
  background-color: var(--danger-color);
  border-color: var(--danger-color);
}

.btn-warning {
  color: var(--text-color);
  background-color: var(--warning-color);
  border-color: var(--warning-color);
}

.btn-info {
  color: #fff;
  background-color: var(--info-color);
  border-color: var(--info-color);
}

/* Status badges */
.badge {
  display: inline-flex;
  align-items: center;
  padding: 0.35em 0.65em;
  font-size: 0.75em;
  font-weight: 700;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 50px;
  gap: 0.25rem;
}

.badge-primary {
  color: #fff;
  background-color: var(--primary-color);
}

.badge-secondary {
  color: var(--text-color);
  background-color: var(--border-color);
}

.badge-success {
  color: #fff;
  background-color: var(--success-color);
}

.badge-danger {
  color: #fff;
  background-color: var(--danger-color);
}

.badge-warning {
  color: var(--text-color);
  background-color: var(--warning-color);
}

.badge-info {
  color: #fff;
  background-color: var(--info-color);
}

/* Alerts */
.alert {
  position: relative;
  padding: 1rem 1rem;
  margin-bottom: 1rem;
  border: 1px solid transparent;
  border-radius: var(--border-radius);
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.alert-icon {
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.alert-success {
  color: #0f5132;
  background-color: #d1e7dd;
  border-color: #badbcc;
}

.alert-danger {
  color: #842029;
  background-color: #f8d7da;
  border-color: #f5c2c7;
}

.alert-warning {
  color: #664d03;
  background-color: #fff3cd;
  border-color: #ffecb5;
}

.alert-info {
  color: #055160;
  background-color: #cff4fc;
  border-color: #b6effb;
}

/* Status logs */
.log-container {
  background-color: #1e293b;
  color: var(--text-white);
  font-family: var(--font-mono);
  padding: 1rem;
  border-radius: var(--border-radius);
  height: 300px;
  overflow-y: auto;
  margin-bottom: 1rem;
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
}

.log-entry {
  margin-bottom: 0.25rem;
  line-height: 1.4;
}

.log-time {
  color: var(--text-light);
  margin-right: 0.5rem;
}

/* Tables */
.table-container {
  width: 100%;
  overflow-x: auto;
  margin-bottom: 1rem;
}

.table {
  width: 100%;
  margin-bottom: 1rem;
  color: var(--text-color);
  vertical-align: top;
  border-color: var(--border-color);
  border-collapse: collapse;
}

.table th,
.table td {
  padding: 0.75rem 1rem;
  vertical-align: middle;
  border-bottom: 1px solid var(--border-color);
  text-align: left;
}

.table thead th {
  vertical-align: bottom;
  background-color: var(--bg-main);
  font-weight: 600;
  color: var(--text-color);
  white-space: nowrap;
}

.table tbody tr:hover {
  background-color: var(--bg-hover);
}

.table-sm th, .table-sm td {
  padding: 0.5rem 0.75rem;
}

/* Stats cards */
.stats-card {
  position: relative;
  padding: 1.5rem;
  overflow: hidden;
}

.stats-card-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: var(--border-radius);
  margin-bottom: 1rem;
  color: #fff;
}

.stats-card-icon.bg-primary {
  background-color: var(--primary-color);
}

.stats-card-icon.bg-success {
  background-color: var(--success-color);
}

.stats-card-icon.bg-warning {
  background-color: var(--warning-color);
}

.stats-card-icon.bg-danger {
  background-color: var(--danger-color);
}

.stats-card-icon.bg-info {
  background-color: var(--info-color);
}

.stats-card-title {
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.25rem;
  color: var(--text-muted);
}

.stats-card-value {
  font-size: 1.75rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.stats-card-trend {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.875rem;
}

.trend-up {
  color: var(--success-color);
}

.trend-down {
  color: var(--danger-color);
}

/* Utilities */
.d-flex {
  display: flex !important;
}

.align-items-center {
  align-items: center !important;
}

.justify-content-between {
  justify-content: space-between !important;
}

.justify-content-center {
  justify-content: center !important;
}

.flex-column {
  flex-direction: column !important;
}

.flex-grow-1 {
  flex-grow: 1 !important;
}

.gap-1 { gap: 0.25rem !important; }
.gap-2 { gap: 0.5rem !important; }
.gap-3 { gap: 0.75rem !important; }
.gap-4 { gap: 1rem !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: 0.25rem !important; }
.mb-2 { margin-bottom: 0.5rem !important; }
.mb-3 { margin-bottom: 1rem !important; }
.mb-4 { margin-bottom: 1.5rem !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: 0.25rem !important; }
.mt-2 { margin-top: 0.5rem !important; }
.mt-3 { margin-top: 1rem !important; }
.mt-4 { margin-top: 1.5rem !important; }

.w-100 { width: 100% !important; }
.h-100 { height: 100% !important; }

.text-center { text-align: center !important; }
.text-right { text-align: right !important; }

.text-muted { color: var(--text-muted) !important; }
.text-primary { color: var(--primary-color) !important; }
.text-success { color: var(--success-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-danger { color: var(--danger-color) !important; }
.text-info { color: var(--info-color) !important; }
.text-white { color: var(--text-white) !important; }

.fw-bold { font-weight: 700 !important; }
.fw-semibold { font-weight: 600 !important; }
.fw-medium { font-weight: 500 !important; }
.fw-normal { font-weight: 400 !important; }

/* noVNC Modal Styles */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.modal-backdrop.show {
  opacity: 1;
  visibility: visible;
}

.modal-container {
  background: var(--bg-card);
  width: 90vw;
  height: 90vh;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  display: flex;
  flex-direction: column;
  transform: scale(0.9);
  opacity: 0;
  transition: all 0.3s ease;
}

.modal-backdrop.show .modal-container {
  transform: scale(1);
  opacity: 1;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.modal-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.modal-close {
  background: transparent;
  border: none;
  cursor: pointer;
  font-size: 1.5rem;
  color: var(--text-muted);
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: var(--transition);
}

.modal-close:hover {
  background: var(--bg-hover);
  color: var(--danger-color);
}

.modal-body {
  flex: 1;
  padding: 0;
  position: relative;
  overflow: hidden;
}

.modal-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.vnc-frame {
  width: 100%;
  height: 100%;
  border: none;
}

.vnc-controls {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.vnc-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.vnc-status-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
}

.vnc-status-indicator.connected {
  background-color: var(--success-color);
}

.vnc-status-indicator.disconnected {
  background-color: var(--danger-color);
}

.vnc-status-indicator.connecting {
  background-color: var(--warning-color);
}

.vnc-shortcuts {
  font-size: 0.875rem;
  color: var(--text-muted);
}

.vnc-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: var(--bg-main);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  color: var(--text-color);
  font-size: 0.875rem;
  cursor: pointer;
  transition: var(--transition);
}

.vnc-btn:hover {
  background-color: var(--border-color);
}

.vnc-btn svg {
  width: 16px;
  height: 16px;
}

.vnc-fullscreen-btn {
  background-color: var(--primary-color);
  color: white;
}

.vnc-fullscreen-btn:hover {
  background-color: var(--primary-dark);
}

/* Tabs */
.tabs {
  display: flex;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 1.5rem;
  overflow-x: auto;
  scrollbar-width: none;
}

.tabs::-webkit-scrollbar {
  display: none;
}

.tab-item {
  padding: 0.75rem 1.25rem;
  font-weight: 500;
  border-bottom: 2px solid transparent;
  color: var(--text-muted);
  cursor: pointer;
  white-space: nowrap;
  transition: var(--transition);
}

.tab-item:hover {
  color: var(--primary-color);
}

.tab-item.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

/* Spinner/Loading */
.spinner {
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.spinner-sm {
  width: 1rem;
  height: 1rem;
  border-width: 2px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: var(--border-radius);
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.fade-in {
  animation: fadeIn 0.3s ease;
}

@keyframes slideIn {
  from { transform: translateY(10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.slide-in {
  animation: slideIn 0.3s ease;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.pulse {
  animation: pulse 2s infinite;
}

/* Dark mode toggling */
body[data-theme="dark"] {
  --bg-main: #121212;
  --bg-card: #1e1e1e;
  --bg-sidebar: #000000;
  --bg-header: #1e1e1e;
  --bg-hover: rgba(255, 255, 255, 0.05);
  
  --border-color: #333;
  
  --text-color: #e0e0e0;
  --text-muted: #aaa;
  --text-light: #888;
}

/* Mobile responsiveness */
@media (max-width: 992px) {
  :root {
    --sidebar-width: 240px;
  }
  
  .col-2, .col-3, .col-4, .col-6, .col-8 {
    flex: 0 0 100%;
    max-width: 100%;
  }
  
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

@media (max-width: 768px) {
  :root {
    --sidebar-width: 0;
    --content-padding: 1rem;
  }
  
  .sidebar {
    transform: translateX(-100%);
  }
  
  .sidebar.mobile-show {
    transform: translateX(0);
    width: var(--sidebar-width);
  }
  
  .main-content {
    margin-left: 0;
  }
  
  .row {
    flex-direction: column;
  }
  
  .mobile-sidebar-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    z-index: 99;
    display: none;
  }
  
  .mobile-sidebar-backdrop.show {
    display: block;
  }
  
  .mobile-menu-toggle {
    display: flex;
    margin-right: 0.5rem;
  }
} 