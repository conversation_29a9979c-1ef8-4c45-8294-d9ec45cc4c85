<!-- Crawl Form Component -->
<div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6" id="crawl-section">
    <div class="flex items-center justify-between border-b border-gray-200 dark:border-gray-700 px-6 py-4">
        <h2 class="text-lg font-medium text-gray-800 dark:text-gray-200 flex items-center">
            <i class="bi bi-rocket-takeoff mr-2 text-blue-600 dark:text-blue-400"></i>
            <span>Khởi chạy Crawl</span>
        </h2>
    </div>
    <div class="p-6">
        <form id="crawlForm" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="url" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">URL</label>
                    <input type="text" id="url" name="url" class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white sm:text-sm" placeholder="Nhập URL của website" required>
                </div>
                <div>
                    <label for="crawlDepth" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Độ sâu Crawl</label>
                    <select id="crawlDepth" name="crawlDepth" class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white sm:text-sm">
                        <option value="1">1 (Chỉ trang hiện tại)</option>
                        <option value="2" selected>2 (Trang hiện tại + links trực tiếp)</option>
                        <option value="3">3 (Trang hiện tại + 2 cấp links)</option>
                        <option value="4">4 (Trang hiện tại + 3 cấp links)</option>
                    </select>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="crawlMode" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Chế độ Crawl</label>
                    <select id="crawlMode" name="crawlMode" class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white sm:text-sm">
                        <option value="breadthFirst" selected>Crawl theo chiều rộng</option>
                        <option value="depthFirst">Crawl theo chiều sâu</option>
                    </select>
                </div>
                <div>
                    <label for="delay" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Độ trễ (ms)</label>
                    <input type="number" id="delay" name="delay" class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white sm:text-sm" value="500" min="0" max="5000" step="100">
                </div>
            </div>
            
            <div>
                <span class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Tùy chọn</span>
                <div class="flex flex-wrap gap-x-6 gap-y-2">
                    <label class="inline-flex items-center">
                        <input type="checkbox" id="followExternalLinks" name="followExternalLinks" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Theo các đường dẫn ngoài</span>
                    </label>
                    <label class="inline-flex items-center">
                        <input type="checkbox" id="respectRobotsTxt" name="respectRobotsTxt" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" checked>
                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Tôn trọng robots.txt</span>
                    </label>
                    <label class="inline-flex items-center">
                        <input type="checkbox" id="captureScreenshots" name="captureScreenshots" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" checked>
                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Chụp màn hình</span>
                    </label>
                </div>
            </div>
            
            <div>
                <label for="customHeaders" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Headers tùy chỉnh (JSON)</label>
                <textarea id="customHeaders" name="customHeaders" rows="3" class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white sm:text-sm" placeholder='{"User-Agent": "Custom Bot", "Accept-Language": "en-US,en;q=0.5"}'></textarea>
            </div>
            
            <div class="flex items-center space-x-3">
                <button type="submit" id="startCrawlBtn" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <i class="bi bi-play-fill mr-2"></i>
                    Bắt đầu Crawl
                </button>
                <button type="reset" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md shadow-sm text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <i class="bi bi-arrow-counterclockwise mr-2"></i>
                    Đặt lại
                </button>
            </div>
        </form>
    </div>
</div> 