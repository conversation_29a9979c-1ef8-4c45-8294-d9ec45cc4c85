<!-- Results Widget Component -->
<div class="card" id="results-section">
    <div class="card-header">
        <div class="card-title">
            <i class="bi bi-file-earmark-text"></i>
            <span>Kết quả & Logs</span>
        </div>
    </div>
    <div class="card-body">
        <div class="results-container">
            <!-- Results tabs -->
            <div class="results-tabs">
                <div class="tab-item active" data-tab="crawl-log">
                    <i class="bi bi-list-columns"></i>
                    <span>Crawl Log</span>
                </div>
                <div class="tab-item" data-tab="urls">
                    <i class="bi bi-link-45deg"></i>
                    <span>URLs</span>
                </div>
                <div class="tab-item" data-tab="errors">
                    <i class="bi bi-exclamation-triangle"></i>
                    <span>Errors</span>
                </div>
            </div>
            
            <!-- Results content -->
            <div class="results-content">
                <div class="tab-content active" id="crawl-log">
                    <div class="log-container" id="crawlLogContainer">
                        <pre class="log-output" id="crawlLog">Waiting for crawl to start...</pre>
                    </div>
                </div>
                
                <div class="tab-content" id="urls">
                    <div class="urls-container" id="urlsContainer">
                        <div class="urls-header">
                            <div class="urls-count">
                                <span class="badge badge-primary" id="urlCount">0</span> URLs
                            </div>
                            <div class="urls-search">
                                <input type="text" class="form-control" placeholder="Tìm kiếm URLs..." id="urlSearch">
                                <i class="bi bi-search"></i>
                            </div>
                        </div>
                        <div class="urls-list" id="urlsList">
                            <!-- URLs will be populated here -->
                        </div>
                    </div>
                </div>
                
                <div class="tab-content" id="errors">
                    <div class="errors-container" id="errorsContainer">
                        <div class="errors-header">
                            <div class="errors-count">
                                <span class="badge badge-danger" id="errorCount">0</span> Errors
                            </div>
                        </div>
                        <div class="errors-list" id="errorsList">
                            <!-- Errors will be populated here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div> 