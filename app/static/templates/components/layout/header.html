<!-- Header Component -->
<header class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 py-3 px-4">
    <div class="flex justify-between items-center">
        <!-- Mobile menu toggle -->
        <button class="lg:hidden text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white focus:outline-none" id="mobile-menu-toggle">
            <i class="bi bi-list text-2xl"></i>
        </button>
        
        <!-- Page title -->
        <div class="flex items-center">
            <h1 class="text-lg font-medium text-gray-800 dark:text-gray-200" id="page-title">Manus Crawler</h1>
            <span class="ml-2 px-2 py-1 bg-blue-600 text-white text-xs font-medium rounded-full">Realtime</span>
        </div>
        
        <!-- Header actions -->
        <div class="flex items-center space-x-4">
            <!-- Theme toggle button -->
            <button class="p-1 rounded-full text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" id="theme-toggle">
                <i class="bi bi-circle-half text-xl"></i>
            </button>
            
            <!-- WebSocket status indicator -->
            <div class="flex items-center space-x-2">
                <div class="h-2 w-2 rounded-full bg-red-500" id="wsStatusIndicator"></div>
                <span class="text-sm text-gray-600 dark:text-gray-300" id="wsStatus">Disconnected</span>
            </div>
        </div>
    </div>
</header>

<!-- Alert container -->
<div id="alertContainer" class="slide-in mt-4 mx-4"></div> 