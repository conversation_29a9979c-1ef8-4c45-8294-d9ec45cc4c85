<!-- <PERSON>ripts Component -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css"></script>
<script src="https://cdn.jsdelivr.net/npm/@novnc/novnc@1.3.0/lib/rfb.min.js"></script>
<script>
    // Utility functions
    function showAlert(message, type = 'success') {
        const alertElement = document.createElement('div');
        alertElement.className = `alert alert-${type}`;
        alertElement.innerHTML = `
            <div class="alert-content">
                <span>${message}</span>
            </div>
            <button class="alert-close">
                <i class="bi bi-x"></i>
            </button>
        `;
        
        const alertContainer = document.getElementById('alertContainer');
        alertContainer.appendChild(alertElement);
        
        // Add event listener to close button
        const closeButton = alertElement.querySelector('.alert-close');
        closeButton.addEventListener('click', () => {
            alertElement.classList.add('fade-out');
            setTimeout(() => {
                alertContainer.removeChild(alertElement);
            }, 300);
        });
        
        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            if (alertElement.parentNode === alertContainer) {
                alertElement.classList.add('fade-out');
                setTimeout(() => {
                    if (alertElement.parentNode === alertContainer) {
                        alertContainer.removeChild(alertElement);
                    }
                }, 300);
            }
        }, 5000);
    }
    
    // Sidebar functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Sidebar toggle
        const sidebar = document.getElementById('sidebar');
        const sidebarToggle = document.getElementById('sidebar-toggle');
        const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
        const mobileSidebarBackdrop = document.getElementById('mobile-sidebar-backdrop');
        
        sidebarToggle.addEventListener('click', () => {
            sidebar.classList.toggle('collapsed');
        });
        
        mobileMenuToggle.addEventListener('click', () => {
            sidebar.classList.toggle('mobile-open');
            mobileSidebarBackdrop.classList.toggle('active');
        });
        
        mobileSidebarBackdrop.addEventListener('click', () => {
            sidebar.classList.remove('mobile-open');
            mobileSidebarBackdrop.classList.remove('active');
        });
        
        // Navigation
        const navLinks = document.querySelectorAll('.menu-link');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                // Remove active class from all links
                navLinks.forEach(l => l.classList.remove('active'));
                
                // Add active class to clicked link
                link.classList.add('active');
                
                // Update page title
                const pageTitle = document.getElementById('page-title');
                pageTitle.textContent = link.querySelector('.menu-text').textContent;
                
                // Close mobile sidebar if open
                if (sidebar.classList.contains('mobile-open')) {
                    sidebar.classList.remove('mobile-open');
                    mobileSidebarBackdrop.classList.remove('active');
                }
            });
        });
        
        // Theme toggle
        const themeToggle = document.getElementById('theme-toggle');
        const htmlElement = document.documentElement;
        
        // Check for saved theme preference
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme) {
            htmlElement.setAttribute('data-theme', savedTheme);
        } else if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            // If no saved preference, use system preference
            htmlElement.setAttribute('data-theme', 'dark');
        }
        
        themeToggle.addEventListener('click', () => {
            const currentTheme = htmlElement.getAttribute('data-theme') || 'light';
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';
            
            htmlElement.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
        });
        
        // Results tabs
        const tabItems = document.querySelectorAll('.tab-item');
        const tabContents = document.querySelectorAll('.tab-content');
        
        tabItems.forEach(tab => {
            tab.addEventListener('click', () => {
                const tabId = tab.getAttribute('data-tab');
                
                // Remove active class from all tabs and contents
                tabItems.forEach(t => t.classList.remove('active'));
                tabContents.forEach(c => c.classList.remove('active'));
                
                // Add active class to clicked tab and corresponding content
                tab.classList.add('active');
                document.getElementById(tabId).classList.add('active');
            });
        });
        
        // VNC Modal
        const openVncModal = document.getElementById('open-vnc-modal');
        const vncModal = document.getElementById('vncModal');
        const vncModalOverlay = document.getElementById('vncModalOverlay');
        const vncCloseBtn = document.getElementById('vncCloseBtn');
        
        openVncModal.addEventListener('click', () => {
            vncModal.classList.add('active');
        });
        
        vncCloseBtn.addEventListener('click', () => {
            vncModal.classList.remove('active');
        });
        
        vncModalOverlay.addEventListener('click', () => {
            vncModal.classList.remove('active');
        });
        
        // Forms submissions
        const crawlForm = document.getElementById('crawlForm');
        const statusForm = document.getElementById('statusForm');
        const chatForm = document.getElementById('chatForm');
        
        if (crawlForm) {
            crawlForm.addEventListener('submit', (e) => {
                e.preventDefault();
                // TODO: Implement crawl form submission
                showAlert('Crawl started successfully!', 'success');
            });
        }
        
        if (statusForm) {
            statusForm.addEventListener('submit', (e) => {
                e.preventDefault();
                // TODO: Implement status form submission
                const statusResult = document.getElementById('statusResult');
                statusResult.style.display = 'block';
                showAlert('Status retrieved successfully!', 'info');
            });
        }
        
        if (chatForm) {
            chatForm.addEventListener('submit', (e) => {
                e.preventDefault();
                const chatInput = document.getElementById('chatInput');
                const message = chatInput.value.trim();
                
                if (message) {
                    const chatMessages = document.getElementById('chatMessages');
                    
                    // Add user message
                    const userMessageDiv = document.createElement('div');
                    userMessageDiv.className = 'chat-message user';
                    userMessageDiv.innerHTML = `
                        <div class="message-content">
                            <p>${message}</p>
                        </div>
                    `;
                    chatMessages.appendChild(userMessageDiv);
                    
                    // Clear input
                    chatInput.value = '';
                    
                    // TODO: Send message to backend and handle response
                    
                    // Simulate response (remove in production)
                    setTimeout(() => {
                        const botMessageDiv = document.createElement('div');
                        botMessageDiv.className = 'chat-message bot';
                        botMessageDiv.innerHTML = `
                            <div class="message-content">
                                <p>This is a placeholder response. In the actual implementation, this would be replaced with the response from the backend.</p>
                            </div>
                        `;
                        chatMessages.appendChild(botMessageDiv);
                        chatMessages.scrollTop = chatMessages.scrollHeight;
                    }, 1000);
                    
                    // Scroll to bottom
                    chatMessages.scrollTop = chatMessages.scrollHeight;
                }
            });
        }
        
        // Initialize with the first page
        document.getElementById('nav-crawl').click();
    });
    
    // noVNC Connection
    let rfb = null;
    
    function connectVNC() {
        const vncConnect = document.getElementById('vncConnect');
        const vncDisconnect = document.getElementById('vncDisconnect');
        const vncStatus = document.getElementById('vncStatus');
        const vncStatusIndicator = document.getElementById('vncStatusIndicator');
        const vncViewOnly = document.getElementById('vncViewOnly');
        const vncContainer = document.getElementById('vnc');
        
        // Remove any existing RFB object
        if (rfb) {
            rfb.disconnect();
            rfb = null;
        }
        
        // Create a new RFB object
        try {
            // Update to the correct WebSocket URL for your noVNC setup
            rfb = new RFB(vncContainer, 'ws://' + window.location.hostname + ':6080/websockify');
            
            rfb.viewOnly = vncViewOnly.checked;
            
            rfb.addEventListener("connect", () => {
                vncStatus.textContent = "Connected";
                vncStatusIndicator.classList.remove('disconnected');
                vncStatusIndicator.classList.add('connected');
                vncConnect.disabled = true;
                vncDisconnect.disabled = false;
                document.getElementById('vncInfo').style.display = 'none';
            });
            
            rfb.addEventListener("disconnect", () => {
                vncStatus.textContent = "Disconnected";
                vncStatusIndicator.classList.remove('connected');
                vncStatusIndicator.classList.add('disconnected');
                vncConnect.disabled = false;
                vncDisconnect.disabled = true;
                document.getElementById('vncInfo').style.display = 'block';
            });
            
        } catch (err) {
            showAlert('Error connecting to VNC: ' + err.message, 'error');
        }
    }
    
    function disconnectVNC() {
        if (rfb) {
            rfb.disconnect();
            rfb = null;
        }
    }
    
    document.getElementById('vncConnect').addEventListener('click', connectVNC);
    document.getElementById('vncDisconnect').addEventListener('click', disconnectVNC);
    document.getElementById('vncViewOnly').addEventListener('change', (e) => {
        if (rfb) {
            rfb.viewOnly = e.target.checked;
        }
    });
    
    document.getElementById('vncFullscreen').addEventListener('click', () => {
        const vncContainer = document.getElementById('vnc');
        if (vncContainer.requestFullscreen) {
            vncContainer.requestFullscreen();
        } else if (vncContainer.mozRequestFullScreen) {
            vncContainer.mozRequestFullScreen();
        } else if (vncContainer.webkitRequestFullscreen) {
            vncContainer.webkitRequestFullscreen();
        } else if (vncContainer.msRequestFullscreen) {
            vncContainer.msRequestFullscreen();
        }
    });
</script> 