<!DOCTYPE html>
<html lang="en" class="h-full">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> - Admin Panel</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css">
    <link rel="stylesheet" href="/static/css/tailwind-custom.css">
    <link rel="stylesheet" href="/static/css/roboto.css">
    <link rel="stylesheet" href="/static/css/main.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <style>
      /* Additional styles for admin layout */
      .container {
        max-width: 800px;
        margin: 40px auto;
        padding: 20px;
        background-color: #f9f9f9;
        border: 1px solid #ddd;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
      .card {
        background-color: #fff;
        padding: 20px;
        margin-bottom: 20px;
        border: 1px solid #ddd;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
      .card h2 {
        margin-top: 0;
      }
      .alert {
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 5px;
        margin-bottom: 20px;
      }
      .alert-success {
        background-color: #dff0d8;
        border-color: #d6e9c6;
        color: #3c763d;
      }
      .alert-danger {
        background-color: #f2dede;
        border-color: #ebccd1;
        color: #a94442;
      }
      
      /* Sidebar Styles */
      .sidebar-link {
        color: #4b5563;
        transition: all 0.2s;
      }
      
      .sidebar-link:hover {
        background-color: #f3f4f6;
        color: #111827;
      }
      
      .dark .sidebar-link {
        color: #e5e7eb;
      }
      
      .dark .sidebar-link:hover {
        background-color: #374151;
        color: #f9fafb;
      }
      
      .sidebar-link.active {
        background-color: #e0e7ff;
        color: #3b82f6;
        font-weight: 600;
      }
      
      .dark .sidebar-link.active {
        background-color: rgba(59, 130, 246, 0.2);
        color: #60a5fa;
      }
      
      /* Content tabs */
      .admin-tab {
        display: none;
      }
      
      .admin-tab.active {
        display: block;
      }
      
      /* Transitions */
      .fade-enter {
        opacity: 0;
      }
      
      .fade-enter-active {
        opacity: 1;
        transition: opacity 0.3s ease-in-out;
      }
      
      /* Mobile menu toggle */
      .sidebar-toggle {
        display: none;
      }
      
      @media (max-width: 768px) {
        .sidebar-toggle {
          display: flex;
        }
        
        .admin-sidebar {
          position: fixed;
          left: -100%;
          width: 280px;
          z-index: 50;
          transition: left 0.3s ease-in-out;
        }
        
        .admin-sidebar.open {
          left: 0;
        }
        
        .admin-content {
          margin-left: 0 !important;
        }
      }
    </style>
</head>
<body class="h-full bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100">
  <div class="flex h-full">
    <!-- Mobile sidebar toggle -->
    <button id="sidebarToggle" class="sidebar-toggle fixed top-4 left-4 z-50 p-2 bg-white dark:bg-gray-800 rounded-md shadow-md">
      <i class="bi bi-list text-xl"></i>
    </button>
    
    <!-- Sidebar -->
    <div id="adminSidebar" class="admin-sidebar w-64 h-full">
      {% include 'components/admin/sidebar.html' %}
    </div>
    
    <!-- Main Content -->
    <div class="admin-content flex-1 flex flex-col h-full overflow-hidden">
      <!-- Header -->
      <header class="bg-white dark:bg-gray-800 shadow-sm">
        <div class="px-6 py-3 flex items-center justify-between">
          <div class="flex items-center">
            <h1 class="text-xl font-bold text-gray-800 dark:text-white flex items-center">
              <span id="currentTabTitle">Dashboard</span>
            </h1>
          </div>
          <div>
            <a href="/" class="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-1 focus:ring-offset-1 focus:ring-indigo-500">
              <i class="bi bi-house-door-fill mr-1.5"></i> Trang chính
            </a>
          </div>
        </div>
      </header>

      <div id="alertContainer" class="px-6 pt-4"></div>
    
      <!-- Main content area -->
      <div class="flex-1 overflow-auto p-6">
        {% block content %}{% endblock %}
      </div>
    </div>
  </div>

  <!-- Modals -->
  {% include 'components/admin/modals/setup-profile.html' %}
  {% include 'components/admin/modals/vnc-modal.html' %}

  <!-- Scripts -->
  {% include 'components/admin/scripts.html' %}
  
  <script>
    // Sidebar functionality
    document.addEventListener('DOMContentLoaded', function() {
      // Mobile sidebar toggle
      const sidebarToggle = document.getElementById('sidebarToggle');
      const adminSidebar = document.getElementById('adminSidebar');
      
      if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function() {
          adminSidebar.classList.toggle('open');
        });
      }
      
      // Initialize with Dashboard tab
      switchTab('dashboard');
    });
    
    function switchTab(tabId) {
      // Update sidebar links
      const links = document.querySelectorAll('.sidebar-link');
      links.forEach(link => {
        link.classList.remove('active');
      });
      
      const activeLink = document.querySelector(`.sidebar-link[href="#${tabId}"]`);
      if (activeLink) {
        activeLink.classList.add('active');
      }
      
      // Update content tabs
      const tabs = document.querySelectorAll('.admin-tab');
      tabs.forEach(tab => {
        tab.classList.remove('active');
      });
      
      const activeTab = document.getElementById(`tab-${tabId}`);
      if (activeTab) {
        activeTab.classList.add('active');
        
        // Update header title
        const currentTabTitle = document.getElementById('currentTabTitle');
        switch(tabId) {
          case 'dashboard':
            currentTabTitle.textContent = 'Dashboard';
            break;
          case 'profiles':
            currentTabTitle.textContent = 'Profile Management';
            break;
          case 'logs':
            currentTabTitle.textContent = 'Status Logs';
            break;
          case 'settings':
            currentTabTitle.textContent = 'Settings';
            break;
          default:
            currentTabTitle.textContent = 'Admin Panel';
        }
      }
      
      // On mobile, close sidebar after selection
      const adminSidebar = document.getElementById('adminSidebar');
      if (window.innerWidth <= 768) {
        adminSidebar.classList.remove('open');
      }
      
      // Update URL hash
      window.location.hash = tabId;
    }
    
    // Check URL hash on page load
    window.addEventListener('load', function() {
      const hash = window.location.hash.substring(1);
      if (hash && ['dashboard', 'profiles', 'logs', 'settings'].includes(hash)) {
        switchTab(hash);
      }
    });
  </script>
</body>
</html> 