<!-- noVNC Modal Component -->
<div class="modal" id="vncModal">
    <div class="modal-overlay" id="vncModalOverlay"></div>
    <div class="modal-container full-screen">
        <div class="modal-header">
            <div class="modal-title">
                <i class="bi bi-display"></i>
                <span>noVNC Remote Control</span>
            </div>
            <div class="vnc-controls">
                <button class="btn btn-icon vnc-control" id="vncConnect" title="Connect">
                    <i class="bi bi-plug-fill"></i>
                </button>
                <button class="btn btn-icon vnc-control" id="vncDisconnect" title="Disconnect">
                    <i class="bi bi-plug"></i>
                </button>
                <button class="btn btn-icon vnc-control" id="vncFullscreen" title="Fullscreen">
                    <i class="bi bi-arrows-fullscreen"></i>
                </button>
            </div>
            <div class="vnc-status">
                <div class="vnc-status-indicator disconnected" id="vncStatusIndicator"></div>
                <span id="vncStatus">Disconnected</span>
            </div>
            <button class="modal-close-btn" id="vncCloseBtn">
                <i class="bi bi-x-lg"></i>
            </button>
        </div>
        <div class="modal-body">
            <div class="vnc-container">
                <div id="vnc"></div>
            </div>
            <div id="vncInfo" class="vnc-info">
                Kết nối noVNC đến container sẽ giúp bạn kiểm soát quá trình crawling trực tiếp.
            </div>
        </div>
        <div class="modal-footer">
            <div class="vnc-options">
                <div class="form-check">
                    <input type="checkbox" id="vncViewOnly" class="form-check-input" checked>
                    <label for="vncViewOnly" class="form-check-label">View Only</label>
                </div>
                <div class="form-check">
                    <input type="checkbox" id="vncClipboard" class="form-check-input">
                    <label for="vncClipboard" class="form-check-label">Enable Clipboard</label>
                </div>
            </div>
        </div>
    </div>
</div> 