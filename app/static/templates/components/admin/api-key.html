<!-- API Key Section -->
<div class="space-y-4">
  <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800/30 rounded-md p-3 flex items-start">
    <div class="flex-shrink-0 text-yellow-600 dark:text-yellow-500">
      <i class="bi bi-exclamation-triangle-fill text-base"></i>
    </div>
    <div class="ml-3">
      <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">Warning</h3>
      <p class="mt-1 text-sm text-yellow-700 dark:text-yellow-300">API Key sẽ được gửi trong header của request. Đảm bảo sử dụng HTTPS hoặc môi trường an toàn để bảo vệ thông tin.</p>
    </div>
  </div>

  <div class="max-w-lg">
    <label for="apiKeyInput" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5">API Key:</label>
    <div class="relative rounded-md shadow-sm">
      <input type="password" id="apiKeyInput" name="apiKeyInput" placeholder="Nhập API Key của bạn..." 
        class="block w-full pr-10 border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white sm:text-sm" />
      <button type="button" onclick="toggleApiKeyVisibility()" class="absolute inset-y-0 right-0 px-3 py-1 text-gray-500 dark:text-gray-400">
        <i id="apiKeyToggleIcon" class="bi bi-eye"></i>
      </button>
    </div>
    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">API Key được sử dụng để xác thực các request.</p>
  </div>

  <div class="pt-2">
    <button type="button" onclick="verifyApiKey()" class="inline-flex items-center px-2.5 py-1.5 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-1 focus:ring-offset-1 focus:ring-blue-500">
      <i class="bi bi-check-circle-fill mr-1.5"></i> Xác thực API Key
    </button>
  </div>
</div>

<script>
  function verifyApiKey() {
    const apiKey = document.getElementById('apiKeyInput').value.trim();
    if (!apiKey) {
      showAlert('Vui lòng nhập API Key để xác thực', 'warning');
      return;
    }
    
    // Demo: Thêm xác thực API key
    showAlert('API Key hợp lệ', 'success');
    
    // Tự động load profiles
    if (typeof listProfiles === 'function') {
      setTimeout(() => listProfiles(), 500);
    }
  }
</script> 