<div id="setupProfileModal" class="fixed inset-0 z-50 hidden">
  <div class="absolute inset-0 bg-black bg-opacity-50"></div>
  <div class="relative max-w-lg w-full mx-auto my-20 bg-white dark:bg-gray-800 rounded-lg shadow-xl overflow-hidden">
    <!-- Modal Header -->
    <div class="flex items-center justify-between border-b border-gray-200 dark:border-gray-700 px-6 py-4">
      <h2 class="text-lg font-medium text-gray-800 dark:text-white flex items-center">
        <i class="bi bi-person-plus mr-2 text-blue-600 dark:text-blue-400"></i> Tạo Profile Chrome Mới
      </h2>
      <button class="rounded-full p-1 hover:bg-gray-200 dark:hover:bg-gray-700 focus:outline-none" onclick="closeSetupProfileModal()">
        <i class="bi bi-x-lg text-gray-500 dark:text-gray-400"></i>
      </button>
    </div>
    
    <!-- Modal Body -->
    <div class="p-6 space-y-4" id="setupProfileStep1">
      <p class="text-sm text-gray-600 dark:text-gray-400">
        Nhập tên cho profile Chrome mới. Profile này sẽ được tạo và mở qua noVNC để bạn có thể thực hiện các thao tác cấu hình.
      </p>
      <div>
        <label for="setupProfileName" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Tên Profile:</label>
        <input type="text" id="setupProfileName" placeholder="Nhập tên profile..." class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white sm:text-sm">
      </div>
      <div>
        <label for="setupProfileUrl" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">URL Khởi đầu (Tùy chọn):</label>
        <input type="text" id="setupProfileUrl" value="https://google.com/" placeholder="URL để mở khi khởi tạo profile..." class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white sm:text-sm">
      </div>
    </div>
    
    <!-- Modal Footer -->
    <div class="border-t border-gray-200 dark:border-gray-700 px-6 py-4 flex justify-end gap-3">
      <button class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md shadow-sm text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" onclick="closeSetupProfileModal()">
        Hủy bỏ
      </button>
      <button class="px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" onclick="startProfileSetup()">
        <i class="bi bi-play-fill mr-2"></i> Bắt đầu thiết lập
      </button>
    </div>
  </div>
</div> 