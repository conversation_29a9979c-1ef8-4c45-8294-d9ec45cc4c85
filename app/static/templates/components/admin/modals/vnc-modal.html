<div id="vncModal" class="fixed inset-0 z-50 hidden">
  <div class="absolute inset-0 bg-black bg-opacity-50"></div>
  <div class="relative max-w-4xl w-full mx-auto my-10 bg-white dark:bg-gray-800 rounded-lg shadow-xl overflow-hidden">
    <!-- <PERSON><PERSON> Header -->
    <div class="flex items-center justify-between border-b border-gray-200 dark:border-gray-700 px-6 py-4">
      <h2 class="text-lg font-medium text-gray-800 dark:text-white flex items-center" id="vncModalTitle">
        <i class="bi bi-display mr-2 text-blue-600 dark:text-blue-400"></i> Thiết lập Profile với noVNC
      </h2>
      <button class="rounded-full p-1 hover:bg-gray-200 dark:hover:bg-gray-700 focus:outline-none" onclick="closeVncModal()">
        <i class="bi bi-x-lg text-gray-500 dark:text-gray-400"></i>
      </button>
    </div>
    
    <!-- Setup Instructions -->
    <div class="bg-blue-50 dark:bg-blue-900/20 border-b border-blue-100 dark:border-blue-900/30 p-4 text-sm text-blue-800 dark:text-blue-200">
      <div class="flex items-start">
        <i class="bi bi-info-circle-fill mt-1 mr-2 flex-shrink-0"></i>
        <div>
          <p><strong>Hướng dẫn thiết lập profile Chrome:</strong></p>
          <ol class="list-decimal pl-5 mt-2 space-y-1">
            <li>Tương tác với trình duyệt qua noVNC để thiết lập profile.</li>
            <li>Đăng nhập vào các tài khoản, thiết lập cài đặt mong muốn.</li>
            <li>Sau khi hoàn tất, nhấn nút "Hoàn thành thiết lập" bên dưới.</li>
            <li>Đừng đóng tab trình duyệt bằng dấu X, hãy sử dụng nút hoàn thành.</li>
          </ol>
        </div>
      </div>
    </div>
    
    <!-- Modal Body -->
    <div class="p-6">
      <iframe id="vncFrame" class="w-full bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded" style="height: 60vh;" src="" allowfullscreen></iframe>
    </div>
    
    <!-- Modal Footer -->
    <div class="border-t border-gray-200 dark:border-gray-700 px-6 py-4 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
      <div class="flex items-center">
        <div id="vncStatusIndicator" class="w-3 h-3 rounded-full bg-gray-400 mr-2"></div>
        <span id="vncStatusText" class="text-sm text-gray-600 dark:text-gray-400">Đang kết nối...</span>
      </div>
      <div class="flex items-center gap-3">
        <button class="inline-flex items-center px-3 py-1 border border-gray-300 dark:border-gray-600 text-sm rounded shadow-sm text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none" onclick="refreshVnc()">
          <i class="bi bi-arrow-clockwise mr-1"></i> Làm mới
        </button>
        <button id="completeSetupButton" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500" onclick="completeProfileSetup()">
          <i class="bi bi-check-lg mr-1"></i> Hoàn thành thiết lập
        </button>
      </div>
    </div>
  </div>
</div> 