<!-- Profile Management Component -->
<div class="space-y-5">
  <!-- Cards Section -->
  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
    <!-- Chrome Profiles Card -->
    <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg overflow-hidden">
      <div class="p-5 flex items-start">
        <div class="shrink-0 flex items-center justify-center h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-800 text-blue-600 dark:text-blue-300">
          <i class="bi bi-info-circle text-lg"></i>
        </div>
        <div class="ml-4">
          <h3 class="font-medium text-blue-800 dark:text-blue-200 text-lg">Quản lý Chrome Profiles</h3>
          <p class="mt-1 text-sm text-blue-700 dark:text-blue-300">
            Tạo và quản lý các profile Chrome để sử dụng cho việc crawl dữ liệu.
          </p>
        </div>
      </div>
    </div>
    
    <!-- <PERSON><PERSON> -->
    <div class="bg-green-50 dark:bg-green-900/20 rounded-lg overflow-hidden">
      <div class="p-5 flex items-start">
        <div class="shrink-0 flex items-center justify-center h-10 w-10 rounded-full bg-green-100 dark:bg-green-800 text-green-600 dark:text-green-300">
          <i class="bi bi-check2-circle text-lg"></i>
        </div>
        <div class="ml-4">
          <h3 class="font-medium text-green-800 dark:text-green-200 text-lg">Tự động lưu Cookie</h3>
          <p class="mt-1 text-sm text-green-700 dark:text-green-300">
            Thực hiện đăng nhập và thiết lập profile một lần duy nhất. Hệ thống sẽ lưu lại cookie.
          </p>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Profile Actions Bar -->
  <div class="flex items-center justify-between bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
    <h3 class="text-lg font-medium text-gray-800 dark:text-white flex items-center">
      <i class="bi bi-list-check mr-2 text-blue-600 dark:text-blue-400"></i> Danh Sách Profiles
    </h3>
    <button class="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" onclick="showSetupProfileModal()">
      <i class="bi bi-plus-circle mr-1.5"></i> Tạo Profile Mới
    </button>
  </div>
  
  <!-- Profile List -->
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
    <div class="p-4 border-b border-gray-200 dark:border-gray-700">
      <div class="relative max-w-sm">
        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <i class="bi bi-search text-gray-400"></i>
        </div>
        <input type="text" name="searchProfile" id="searchProfile" class="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white sm:text-sm" placeholder="Tìm kiếm profile...">
      </div>
    </div>
    
    <div class="p-4">
      <div id="profileList" class="bg-gray-50 dark:bg-gray-700/50 rounded-md overflow-hidden border border-gray-200 dark:border-gray-700 mb-4">
        <div class="flex items-center justify-center p-6 text-gray-500 dark:text-gray-400">
          <i class="bi bi-arrow-repeat animate-spin mr-2"></i> Đang tải danh sách profile...
        </div>
      </div>
      
      <!-- Profile Status Bar -->
      <div class="flex flex-wrap justify-between items-center mt-3 gap-2">
        <div class="text-sm text-gray-500 dark:text-gray-400">
          <span id="profileCount">0</span> profiles được tìm thấy
        </div>
        <div class="flex gap-2">
          <button class="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md shadow-sm text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-1 focus:ring-offset-1 focus:ring-blue-500" onclick="listProfiles()">
            <i class="bi bi-arrow-clockwise mr-1.5"></i> Làm mới
          </button>
          <button class="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md shadow-sm text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-1 focus:ring-offset-1 focus:ring-blue-500" onclick="importProfiles()">
            <i class="bi bi-upload mr-1.5"></i> Import
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  // Enhance profile search function
  document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchProfile');
    if (searchInput) {
      searchInput.addEventListener('input', function() {
        const query = this.value.toLowerCase().trim();
        filterProfiles(query);
      });
    }
  });
  
  function filterProfiles(query) {
    const rows = document.querySelectorAll('#profileList table tbody tr');
    let visibleCount = 0;
    
    rows.forEach(row => {
      const profileName = row.querySelector('td:first-child').textContent.toLowerCase();
      
      if (query === '' || profileName.includes(query)) {
        row.style.display = '';
        visibleCount++;
      } else {
        row.style.display = 'none';
      }
    });
    
    // Update count
    document.getElementById('profileCount').textContent = visibleCount;
  }
  
  function importProfiles() {
    // Demo functionality
    showAlert('Import functionality will be available in a future update.', 'info');
  }
  
  // Override displayProfiles to add count
  const originalDisplayProfiles = window.displayProfiles;
  window.displayProfiles = function(data) {
    originalDisplayProfiles(data);
    
    // Update count
    if (data && data.profiles) {
      document.getElementById('profileCount').textContent = data.profiles.length;
    } else {
      document.getElementById('profileCount').textContent = '0';
    }
  };
</script> 