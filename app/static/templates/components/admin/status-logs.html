<!-- Status Logs Component -->
<div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
  <div class="border-b border-gray-200 dark:border-gray-700 px-5 py-3 flex items-center justify-between">
    <h2 class="text-lg font-medium text-gray-800 dark:text-white flex items-center">
      <i class="bi bi-terminal mr-2 text-blue-600 dark:text-blue-400"></i> Status Logs
    </h2>
    <div class="flex items-center space-x-2">
      <button class="inline-flex items-center px-2.5 py-1.5 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md shadow-sm text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none" onclick="filterLogs('all')">
        <i class="bi bi-funnel mr-1.5"></i> T<PERSON><PERSON> c<PERSON>
      </button>
      <button class="inline-flex items-center px-2.5 py-1.5 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-1 focus:ring-offset-1 focus:ring-red-500" onclick="clearStatusLog()">
        <i class="bi bi-trash mr-1.5"></i> Xóa Logs
      </button>
    </div>
  </div>
  
  <div class="p-4">
    <div id="statusFilters" class="flex flex-wrap gap-2 mb-3">
      <button class="inline-flex items-center py-1 px-2 bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 rounded text-xs font-medium">
        <i class="bi bi-info-circle mr-1"></i> Info
      </button>
      <button class="inline-flex items-center py-1 px-2 bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300 rounded text-xs font-medium">
        <i class="bi bi-check-circle mr-1"></i> Success
      </button>
      <button class="inline-flex items-center py-1 px-2 bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300 rounded text-xs font-medium">
        <i class="bi bi-exclamation-triangle mr-1"></i> Warning
      </button>
      <button class="inline-flex items-center py-1 px-2 bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300 rounded text-xs font-medium">
        <i class="bi bi-x-circle mr-1"></i> Error
      </button>
    </div>
    
    <div id="statusLog" class="h-64 overflow-y-auto bg-gray-50 dark:bg-gray-700/50 rounded-md border border-gray-200 dark:border-gray-700 font-mono text-sm">
      <!-- Log entries will be inserted here by JavaScript -->
      <div class="flex flex-col p-4 space-y-1.5 text-gray-600 dark:text-gray-400">
        <div class="log-entry">
          <span class="font-mono text-xs text-gray-500 dark:text-gray-400 mr-2">[12:50:07 AM]</span>
          <span class="text-green-600 dark:text-green-400">✅ Admin dashboard loaded</span>
        </div>
      </div>
    </div>
    
    <div class="mt-3 flex justify-end">
      <button class="inline-flex items-center px-2.5 py-1.5 border border-gray-300 dark:border-gray-600 text-sm rounded shadow-sm text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none" onclick="exportLogs()">
        <i class="bi bi-download mr-1"></i> Export Logs
      </button>
    </div>
  </div>
</div>

<script>
  function filterLogs(type) {
    // Demo implementation - in real app would filter logs by type
    showAlert(`Filtering logs by: ${type}`, 'info');
  }
  
  function exportLogs() {
    // Demo functionality - would export logs in production
    const logContent = document.getElementById('statusLog').innerText;
    const blob = new Blob([logContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `system-logs-${new Date().toISOString().slice(0,10)}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    showAlert('Logs exported successfully', 'success');
  }
</script> 