<script>
  // Theme Toggling
  function loadTheme() {
    const theme = localStorage.getItem('theme') || 'light';
    document.documentElement.classList.toggle('dark', theme === 'dark');
    
    if (theme === 'dark') {
      document.documentElement.style.colorScheme = 'dark';
    } else {
      document.documentElement.style.colorScheme = 'light';
    }
  }
  
  function toggleTheme() {
    const currentTheme = localStorage.getItem('theme') || 'light';
    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
    
    localStorage.setItem('theme', newTheme);
    loadTheme();
  }

  // API Key visibility toggle
  function toggleApiKeyVisibility() {
    const apiKeyInput = document.getElementById('apiKeyInput');
    const apiKeyToggleIcon = document.getElementById('apiKeyToggleIcon');
    
    if (apiKeyInput.type === 'password') {
      apiKeyInput.type = 'text';
      apiKeyToggleIcon.classList.remove('bi-eye');
      apiKeyToggleIcon.classList.add('bi-eye-slash');
    } else {
      apiKeyInput.type = 'password';
      apiKeyToggleIcon.classList.remove('bi-eye-slash');
      apiKeyToggleIcon.classList.add('bi-eye');
    }
  }

  // Setup Profile Modal
  function showSetupProfileModal() {
    document.getElementById('setupProfileModal').classList.remove('hidden');
    document.getElementById('setupProfileName').focus();
  }
  
  function closeSetupProfileModal() {
    document.getElementById('setupProfileModal').classList.add('hidden');
  }
  
  // VNC Modal
  let currentSetupProfileName = ''; // Lưu tên profile đang được thiết lập
  
  async function showVncModal(profileName, setupUrl) {
    const modal = document.getElementById('vncModal');
    const frame = document.getElementById('vncFrame');
    const modalTitle = document.getElementById('vncModalTitle');
    
    // Lưu tên profile đang thiết lập
    currentSetupProfileName = profileName;
    
    // Cập nhật tiêu đề modal
    modalTitle.innerHTML = `<i class="bi bi-display mr-2 text-blue-600 dark:text-blue-400"></i> Thiết lập Profile: ${profileName}`;
    
    // Set VNC URL - URL được config để kết nối tới container noVNC
    // Use noVNC with auto-connect, scaling, and appropriate quality for interaction
    frame.src = '//' + window.location.hostname + ':6080/vnc.html?autoconnect=true&resize=scale&quality=6&path=' + btoa('localhost:5900');
    

    // Connect to WebSocket for realtime profile status updates
    // connectToWebSocket(profileName); //TODO: connect to websocket
    const apiKey = document.getElementById('apiKeyInput').value.trim();

    const response = await fetch('/admin/open-profile/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-KEY': apiKey
      },
      body: JSON.stringify({
        profile_name: profileName,
      })
    });
    
    // Set status là đang kết nối
    updateVncStatus('connecting');
    
    // Hiện modal
    modal.classList.remove('hidden');
    
    // Kiểm tra trạng thái kết nối định kỳ
    checkVncConnection();
  }
  
  function closeVncModal() {
    const modal = document.getElementById('vncModal');
    const frame = document.getElementById('vncFrame');
    
    // Close WebSocket if it exists
    if (window.profileWebSocket && window.profileWebSocket.readyState === WebSocket.OPEN) {
      window.profileWebSocket.close();
    }
    
    // Ẩn modal
    modal.classList.add('hidden');
    
    // Xóa src của iframe
    setTimeout(() => {
      frame.src = '';
      currentSetupProfileName = ''; // Xóa tên profile hiện tại
    }, 300);
  }
  
  function updateVncStatus(status) {
    const indicator = document.getElementById('vncStatusIndicator');
    const text = document.getElementById('vncStatusText');
    
    // Xóa tất cả các class hiện tại trừ kích thước và độ tròn
    indicator.className = 'w-3 h-3 rounded-full';
    
    switch (status) {
      case 'connected':
        indicator.classList.add('bg-green-500');
        text.textContent = 'Đã kết nối';
        break;
      case 'disconnected':
        indicator.classList.add('bg-red-500');
        text.textContent = 'Mất kết nối';
        break;
      case 'connecting':
        indicator.classList.add('bg-yellow-400');
        text.textContent = 'Đang kết nối...';
        break;
      default:
        indicator.classList.add('bg-gray-400');
        text.textContent = 'Không xác định';
    }
  }
  
  function checkVncConnection() {
    // Trong thực tế, bạn sẽ kiểm tra kết nối thật sự
    // Ở đây chúng ta giả lập kết nối sau 2 giây
    setTimeout(() => {
      updateVncStatus('connected');
    }, 2000);
  }
  
  function refreshVnc() {
    const frame = document.getElementById('vncFrame');
    updateVncStatus('connecting');
    frame.src = frame.src;
    checkVncConnection();
  }

  // Status log functions
  function logStatus(message) {
    const statusLog = document.getElementById('statusLog');
    const now = new Date().toLocaleTimeString();
    const logEntry = document.createElement('div');
    logEntry.className = 'py-1 border-b border-gray-200 dark:border-gray-700 last:border-0';
    
    logEntry.innerHTML = `<span class="font-mono text-xs text-gray-500 dark:text-gray-400 mr-2">[${now}]</span> ${message}`;
    statusLog.appendChild(logEntry);
    statusLog.scrollTop = statusLog.scrollHeight;
  }

  function clearStatusLog() {
    document.getElementById('statusLog').innerHTML = '';
    showAlert('Logs đã được xóa', 'success');
  }

  // Alert functions
  function showAlert(message, type) {
    const alertContainer = document.getElementById('alertContainer');
    const alertDiv = document.createElement('div');
    
    let bgColor, textColor, borderColor, icon;
    
    switch(type) {
      case 'success':
        bgColor = 'bg-green-50 dark:bg-green-900/20';
        textColor = 'text-green-800 dark:text-green-200';
        borderColor = 'border-green-200 dark:border-green-800/30';
        icon = '<i class="bi bi-check-circle-fill"></i>';
        break;
      case 'danger':
        bgColor = 'bg-red-50 dark:bg-red-900/20';
        textColor = 'text-red-800 dark:text-red-200';
        borderColor = 'border-red-200 dark:border-red-800/30';
        icon = '<i class="bi bi-exclamation-circle-fill"></i>';
        break;
      case 'warning':
        bgColor = 'bg-yellow-50 dark:bg-yellow-900/20';
        textColor = 'text-yellow-800 dark:text-yellow-200';
        borderColor = 'border-yellow-200 dark:border-yellow-800/30';
        icon = '<i class="bi bi-exclamation-triangle-fill"></i>';
        break;
      default:
        bgColor = 'bg-blue-50 dark:bg-blue-900/20';
        textColor = 'text-blue-800 dark:text-blue-200';
        borderColor = 'border-blue-200 dark:border-blue-800/30';
        icon = '<i class="bi bi-info-circle-fill"></i>';
    }
    
    alertDiv.className = `p-4 mb-4 flex items-center gap-3 rounded-lg border ${bgColor} ${textColor} ${borderColor} transform transition-all duration-300 ease-in-out opacity-0 translate-y-[-10px]`;
    alertDiv.innerHTML = `${icon} <span>${message}</span>`;
    
    alertContainer.appendChild(alertDiv);
    
    // Animation
    setTimeout(() => {
      alertDiv.classList.remove('opacity-0', 'translate-y-[-10px]');
    }, 10);
    
    // Auto-remove after delay
    setTimeout(() => {
      alertDiv.classList.add('opacity-0', 'translate-y-[-10px]');
      setTimeout(() => alertDiv.remove(), 300);
    }, 5000);
  }

  // Profile Setup Functions
  async function startProfileSetup() {
    const profileName = document.getElementById('setupProfileName').value.trim();
    const profileUrl = document.getElementById('setupProfileUrl').value.trim() || 'https://google.com/';
    const apiKey = document.getElementById('apiKeyInput').value.trim();

    if (!apiKey) {
      showAlert('Vui lòng nhập API Key để thiết lập profile!', 'danger');
      return;
    }

    if (!profileName) {
      showAlert('Vui lòng nhập tên profile!', 'danger');
      return;
    }

    logStatus(`🚀 Bắt đầu thiết lập profile: ${profileName}`);
    
    try {
      // Đóng modal setup
      closeSetupProfileModal();
      
      // Gọi API để bắt đầu thiết lập profile
      const response = await fetch('/admin/setup-chrome-profile/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-KEY': apiKey
        },
        body: JSON.stringify({
          profile_name: profileName,
          url: profileUrl
        })
      });

      const result = await response.json();

      if (response.ok && result.success) {
        logStatus(`✅ Đã khởi tạo profile '${profileName}' thành công`);
        showAlert(`Đã khởi tạo profile '${profileName}'. Hãy tiếp tục thiết lập qua noVNC.`, 'success');
        
        // Hiện modal noVNC để người dùng tương tác
        showVncModal(profileName, profileUrl);
      } else {
        if (response.status === 401) {
          logStatus(`❌ Lỗi: API Key không hợp lệ hoặc không có quyền`);
          showAlert('API Key không hợp lệ hoặc không có quyền', 'danger');
        } else {
          logStatus(`❌ Lỗi khi thiết lập profile: ${result.error || 'Unknown error'}`);
          showAlert(`Lỗi: ${result.error || result.message || 'Unknown error'}`, 'danger');
        }
      }
    } catch (error) {
      logStatus(`❌ Lỗi: ${error.message}`);
      showAlert(`Lỗi: ${error.message}`, 'danger');
    }
  }
  
  async function completeProfileSetup() {
    if (!currentSetupProfileName) {
      showAlert('Không tìm thấy thông tin profile đang thiết lập!', 'danger');
      return;
    }

    const apiKey = document.getElementById('apiKeyInput').value.trim();
    if (!apiKey) {
      showAlert('Vui lòng nhập API Key để hoàn thành thiết lập profile!', 'danger');
      return;
    }

    logStatus(`🏁 Đang hoàn thành thiết lập profile: ${currentSetupProfileName}`);
    
    const completeBtn = document.getElementById('completeSetupButton');
    completeBtn.disabled = true;
    completeBtn.innerHTML = '<i class="bi bi-hourglass-split animate-spin mr-1"></i> Đang hoàn thành...';
    
    try {
      // Gọi API để hoàn thành thiết lập profile
      const response = await fetch(`/admin/complete-profile-setup/${currentSetupProfileName}`, {
        method: 'POST',
        headers: {
          'X-API-KEY': apiKey
        }
      });

      const result = await response.json();

      if (response.ok && result.success) {
        logStatus(`✅ Profile '${currentSetupProfileName}' đã được thiết lập thành công`);
        showAlert(`Profile '${currentSetupProfileName}' đã được thiết lập thành công`, 'success');
        
        // Đóng modal noVNC
        closeVncModal();
        
        // Làm mới danh sách profile
        listProfiles();
      } else {
        completeBtn.disabled = false;
        completeBtn.innerHTML = '<i class="bi bi-check-lg mr-1"></i> Hoàn thành thiết lập';
        
        if (response.status === 401) {
          logStatus(`❌ Lỗi: API Key không hợp lệ hoặc không có quyền`);
          showAlert('API Key không hợp lệ hoặc không có quyền', 'danger');
        } else {
          logStatus(`❌ Lỗi khi hoàn thành thiết lập: ${result.error || 'Unknown error'}`);
          showAlert(`Lỗi: ${result.error || result.message || 'Unknown error'}`, 'danger');
        }
      }
    } catch (error) {
      completeBtn.disabled = false;
      completeBtn.innerHTML = '<i class="bi bi-check-lg mr-1"></i> Hoàn thành thiết lập';
      logStatus(`❌ Lỗi: ${error.message}`);
      showAlert(`Lỗi: ${error.message}`, 'danger');
    }
  }

  async function listProfiles() {
    const apiKey = document.getElementById('apiKeyInput').value.trim();

    if (!apiKey) {
      showAlert('Vui lòng nhập API Key để xem danh sách profile!', 'danger');
      return;
    }

    logStatus('📋 Đang tải danh sách profiles...');
    
    // Hiển thị loading indicator
    document.getElementById('profileList').innerHTML = `
      <div class="flex items-center justify-center p-6 text-gray-500 dark:text-gray-400">
        <i class="bi bi-arrow-repeat animate-spin mr-2"></i> Đang tải danh sách profile...
      </div>
    `;
    
    try {
      const response = await fetch('/admin/list-profiles/', {
        headers: {
          'X-API-KEY': apiKey
        }
      });

      if (response.ok) {
        const result = await response.json();
        
        if (result.success) {
          const profileCount = result.profiles ? result.profiles.length : 0;
          logStatus(`✅ Đã tải ${profileCount} profiles`);
          displayProfiles(result);
        } else {
          logStatus(`❌ Lỗi: ${result.error || 'Unknown error'}`);
          showAlert(`Lỗi: ${result.error || 'Unknown error'}`, 'danger');
          document.getElementById('profileList').innerHTML = `
            <div class="flex items-center justify-center p-6 text-red-500 dark:text-red-400">
              <i class="bi bi-x-circle mr-2"></i> Không thể tải danh sách profile
            </div>
          `;
        }
      } else {
        if (response.status === 401) {
          logStatus(`❌ Lỗi: API Key không hợp lệ hoặc không có quyền`);
          showAlert('API Key không hợp lệ hoặc không có quyền', 'danger');
          document.getElementById('profileList').innerHTML = `
            <div class="flex items-center justify-center p-6 text-red-500 dark:text-red-400">
              <i class="bi bi-x-circle mr-2"></i> Không thể tải danh sách profile - Không có quyền
            </div>
          `;
        } else {
          logStatus('❌ Lỗi khi lấy danh sách profile');
          showAlert('Lỗi khi lấy danh sách profile', 'danger');
          document.getElementById('profileList').innerHTML = `
            <div class="flex items-center justify-center p-6 text-red-500 dark:text-red-400">
              <i class="bi bi-x-circle mr-2"></i> Không thể tải danh sách profile
            </div>
          `;
        }
      }
    } catch (error) {
      logStatus(`❌ Lỗi: ${error.message}`);
      showAlert(`Lỗi: ${error.message}`, 'danger');
      document.getElementById('profileList').innerHTML = `
        <div class="flex items-center justify-center p-6 text-red-500 dark:text-red-400">
          <i class="bi bi-x-circle mr-2"></i> Không thể tải danh sách profile: ${error.message}
        </div>
      `;
    }
  }

  function displayProfiles(data) {
    const profileList = document.getElementById('profileList');
    profileList.innerHTML = '';

    if (!data || !data.profiles || data.profiles.length === 0) {
      profileList.innerHTML = `
        <div class="flex items-center justify-center p-6 text-gray-500 dark:text-gray-400">
          <i class="bi bi-folder-x mr-2"></i> Không có profile nào. Hãy tạo profile mới.
        </div>
      `;
      return;
    }

    // Tạo table để hiển thị profiles
    const table = document.createElement('table');
    table.className = 'min-w-full divide-y divide-gray-200 dark:divide-gray-700';
    
    // Tạo header
    const thead = document.createElement('thead');
    thead.className = 'bg-gray-50 dark:bg-gray-800';
    thead.innerHTML = `
      <tr>
        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Tên Profile</th>
        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Ngày tạo</th>
        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Kích thước</th>
        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Thao tác</th>
      </tr>
    `;
    table.appendChild(thead);
    
    // Tạo body
    const tbody = document.createElement('tbody');
    tbody.className = 'bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700';
    
    data.profiles.forEach((profile, index) => {
      const row = document.createElement('tr');
      row.className = index % 2 === 0 ? 'bg-white dark:bg-gray-800' : 'bg-gray-50 dark:bg-gray-700/50';
      
      row.innerHTML = `
        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
          <div class="flex items-center">
            <i class="bi bi-person-circle text-blue-500 dark:text-blue-400 mr-2"></i>
            ${profile.name}
          </div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
          ${profile.created || 'N/A'}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
          ${profile.size || 'N/A'}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
          <div class="flex items-center justify-end gap-2">
            <button class="inline-flex items-center p-1.5 border border-transparent text-xs rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none" 
                    title="Mở trong noVNC" onclick="showVncModal('${profile.name}')">
              <i class="bi bi-display"></i>
            </button>
            <button class="inline-flex items-center p-1.5 border border-transparent text-xs rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none" 
                    title="Xóa profile" onclick="deleteProfile('${profile.name}')">
              <i class="bi bi-trash"></i>
            </button>
          </div>
        </td>
      `;
      
      tbody.appendChild(row);
    });
    
    table.appendChild(tbody);
    profileList.appendChild(table);
  }

  async function deleteProfile(profileName) {
    if (!confirm(`Bạn có chắc muốn xóa profile '${profileName}'?`)) {
      return;
    }

    const apiKey = document.getElementById('apiKeyInput').value.trim();

    if (!apiKey) {
      showAlert('Vui lòng nhập API Key để xóa profile!', 'danger');
      return;
    }

    logStatus(`🗑️ Đang xóa profile: ${profileName}`);

    try {
      const response = await fetch(`/admin/delete-profile/${profileName}`, {
        method: 'DELETE',
        headers: {
          'X-API-KEY': apiKey
        }
      });

      const result = await response.json();

      if (response.ok && result.success) {
        logStatus(`✅ Profile '${profileName}' đã được xóa thành công`);
        showAlert(`Profile '${profileName}' đã được xóa thành công`, 'success');
        listProfiles();
      } else {
        if (response.status === 401) {
          logStatus(`❌ Lỗi: API Key không hợp lệ hoặc không có quyền`);
          showAlert('API Key không hợp lệ hoặc không có quyền', 'danger');
        } else {
          logStatus(`❌ Lỗi khi xóa profile: ${result.error || 'Unknown error'}`);
          showAlert(`Lỗi: ${result.error || result.message || 'Unknown error'}`, 'danger');
        }
      }
    } catch (error) {
      logStatus(`❌ Lỗi: ${error.message}`);
      showAlert(`Lỗi: ${error.message}`, 'danger');
    }
  }

  // WebSocket connection for profile setup
  function connectToWebSocket(profileName) {
    // Close any existing WebSocket connection
    if (window.profileWebSocket && window.profileWebSocket.readyState === WebSocket.OPEN) {
      window.profileWebSocket.close();
    }
    
    // Create new WebSocket connection
    const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${wsProtocol}//${window.location.host}/ws/${profileName}`;
    
    window.profileWebSocket = new WebSocket(wsUrl);
    
    window.profileWebSocket.onopen = () => {
      console.log(`WebSocket connected for profile: ${profileName}`);
      logStatus(`📡 WebSocket kết nối thành công cho profile '${profileName}'`);
    };
    
    window.profileWebSocket.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        if (data.type === 'progress') {
          logStatus(`ℹ️ ${data.message}`);
          // Add notification to VNC modal if it's open
          const vncStatus = document.getElementById('vncStatusText');
          if (vncStatus) {
            vncStatus.textContent = data.message;
          }
        } else if (data.type === 'error') {
          logStatus(`❌ Lỗi: ${data.message}`);
          showAlert(data.message, 'danger');
        } else if (data.type === 'data') {
          logStatus(`📊 Nhận dữ liệu từ server cho profile '${profileName}'`);
        }
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
      }
    };
    
    window.profileWebSocket.onclose = () => {
      console.log(`WebSocket closed for profile: ${profileName}`);
      logStatus(`🔌 WebSocket ngắt kết nối cho profile '${profileName}'`);
    };
    
    window.profileWebSocket.onerror = (error) => {
      console.error('WebSocket error:', error);
      logStatus(`❌ WebSocket error cho profile '${profileName}'`);
      showAlert('Lỗi kết nối WebSocket', 'danger');
    };
  }

  // Initialize
  document.addEventListener('DOMContentLoaded', function() {
    loadTheme();
    logStatus('🚀 Admin dashboard loaded');
    
    // Auto-focus on API key input
    document.getElementById('apiKeyInput').focus();
    
    // Try to list profiles if API key is in localStorage
    const savedApiKey = localStorage.getItem('apiKey');
    if (savedApiKey) {
      document.getElementById('apiKeyInput').value = savedApiKey;
      listProfiles();
    }
    
    // Save API key to localStorage when input changes
    document.getElementById('apiKeyInput').addEventListener('change', function() {
      const apiKey = this.value.trim();
      if (apiKey) {
        localStorage.setItem('apiKey', apiKey);
      }
    });
  });
</script>

<style>
  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
  .animate-spin {
    animation: spin 1s linear infinite;
    display: inline-block;
  }
</style> 