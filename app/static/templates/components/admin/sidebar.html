<div class="h-full bg-white dark:bg-gray-800 shadow-md flex flex-col">
  <!-- Logo -->
  <div class="p-3 border-b border-gray-200 dark:border-gray-700">
    <div class="flex items-center justify-center">
      <i class="bi bi-gear-fill text-blue-600 dark:text-blue-400 text-xl"></i>
      <span class="ml-2 font-bold text-gray-800 dark:text-white text-base">Admin Panel</span>
    </div>
    <div class="mt-1 text-center">
      <span class="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs font-medium px-2 py-0.5 rounded-full">v1.0.0</span>
    </div>
  </div>
  
  <!-- Navigation -->
  <nav class="flex-1 overflow-y-auto py-3">
    <ul class="space-y-0.5 px-2">
      <li>
        <a href="#dashboard" onclick="switchTab('dashboard')" class="sidebar-link active flex items-center px-3 py-2 text-sm font-medium rounded-lg group">
          <i class="bi bi-speedometer2 mr-2"></i>
          <span>Dashboard</span>
        </a>
      </li>
      <li>
        <a href="#profiles" onclick="switchTab('profiles')" class="sidebar-link flex items-center px-3 py-2 text-sm font-medium rounded-lg group">
          <i class="bi bi-person-lines-fill mr-2"></i>
          <span>Profile Management</span>
        </a>
      </li>
      <li>
        <a href="#logs" onclick="switchTab('logs')" class="sidebar-link flex items-center px-3 py-2 text-sm font-medium rounded-lg group">
          <i class="bi bi-terminal mr-2"></i>
          <span>Status Logs</span>
        </a>
      </li>
      <li>
        <a href="#settings" onclick="switchTab('settings')" class="sidebar-link flex items-center px-3 py-2 text-sm font-medium rounded-lg group">
          <i class="bi bi-sliders mr-2"></i>
          <span>Settings</span>
        </a>
      </li>
    </ul>
  </nav>
  
  <!-- User -->
  <div class="p-3 border-t border-gray-200 dark:border-gray-700">
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-2">
        <div class="flex-shrink-0">
          <div class="h-7 w-7 rounded-full bg-blue-600 flex items-center justify-center">
            <i class="bi bi-person-fill text-white text-xs"></i>
          </div>
        </div>
        <div>
          <p class="text-sm font-medium text-gray-800 dark:text-white">Admin User</p>
          <p class="text-xs text-gray-500 dark:text-gray-400">Administrator</p>
        </div>
      </div>
      <div class="flex items-center">
        <button class="p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700" onclick="toggleTheme()" aria-label="Toggle theme">
          <i class="bi bi-circle-half text-gray-600 dark:text-gray-400"></i>
        </button>
        <a href="/" class="p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 ml-1" aria-label="Home page">
          <i class="bi bi-house-door text-gray-600 dark:text-gray-400"></i>
        </a>
      </div>
    </div>
  </div>
</div> 