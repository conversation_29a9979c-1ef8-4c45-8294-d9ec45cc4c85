<header class="bg-white dark:bg-gray-800 shadow-sm rounded-lg mb-6">
  <div class="p-6 flex flex-col md:flex-row md:items-center md:justify-between gap-4">
    <div class="flex items-center gap-3">
      <h1 class="text-2xl font-bold text-gray-800 dark:text-white flex items-center">
        <i class="bi bi-gear-fill text-blue-600 dark:text-blue-400 mr-2"></i> Admin Dashboard
      </h1>
      <span class="px-2 py-1 bg-blue-600 text-xs font-medium rounded-full text-white">v1.0.0</span>
    </div>
    <div class="flex items-center gap-3">
      <button class="p-2 rounded-md bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors" onclick="toggleTheme()">
        <i class="bi bi-circle-half"></i>
      </button>
      <a href="/" class="flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md shadow-sm text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
        <i class="bi bi-house-fill mr-2"></i> Quay về trang chính
      </a>
    </div>
  </div>
</header> 