{% extends 'components/layout/admin-layout.html' %}

{% block content %}
<!-- Dashboard Tab -->
<div id="tab-dashboard" class="admin-tab active space-y-5">
  <div class="grid grid-cols-1 md:grid-cols-3 gap-5">
    <div class="bg-white dark:bg-gray-800 p-5 rounded-lg shadow-sm">
      <div class="flex items-center">
        <div class="p-3 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400">
          <i class="bi bi-person-badge text-xl"></i>
        </div>
        <div class="ml-4">
          <h3 class="text-lg font-medium text-gray-700 dark:text-gray-200">Profiles</h3>
          <p class="text-2xl font-semibold text-gray-900 dark:text-white" id="dashProfileCount">0</p>
        </div>
      </div>
    </div>
    
    <div class="bg-white dark:bg-gray-800 p-5 rounded-lg shadow-sm">
      <div class="flex items-center">
        <div class="p-3 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400">
          <i class="bi bi-check2-circle text-xl"></i>
        </div>
        <div class="ml-4">
          <h3 class="text-lg font-medium text-gray-700 dark:text-gray-200">Status</h3>
          <p class="text-2xl font-semibold text-gray-900 dark:text-white">Active</p>
        </div>
      </div>
    </div>
    
    <div class="bg-white dark:bg-gray-800 p-5 rounded-lg shadow-sm">
      <div class="flex items-center">
        <div class="p-3 rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400">
          <i class="bi bi-clock-history text-xl"></i>
        </div>
        <div class="ml-4">
          <h3 class="text-lg font-medium text-gray-700 dark:text-gray-200">Last Activity</h3>
          <p class="text-sm font-medium text-gray-900 dark:text-white" id="lastActivityTime">-</p>
        </div>
      </div>
    </div>
  </div>

  <!-- API Key Card -->
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
    <div class="border-b border-gray-200 dark:border-gray-700 px-5 py-3">
      <h2 class="text-lg font-medium text-gray-800 dark:text-white flex items-center">
        <i class="bi bi-key-fill mr-2 text-blue-600 dark:text-blue-400"></i> API Key Authentication
      </h2>
    </div>
    <div class="p-5">
      {% include 'components/admin/api-key.html' %}
    </div>
  </div>
  
  <!-- Recent Activity -->
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
    <div class="border-b border-gray-200 dark:border-gray-700 px-5 py-3">
      <h2 class="text-lg font-medium text-gray-800 dark:text-white flex items-center">
        <i class="bi bi-activity mr-2 text-blue-600 dark:text-blue-400"></i> Recent Activity
      </h2>
    </div>
    <div class="p-5">
      <div id="recentActivityList" class="space-y-3">
        <p class="text-gray-500 dark:text-gray-400 text-center py-4">
          No recent activity to display
        </p>
      </div>
    </div>
  </div>
</div>

<!-- Profiles Tab -->
<div id="tab-profiles" class="admin-tab">
  {% include 'components/admin/profile-management.html' %}
</div>

<!-- Logs Tab -->
<div id="tab-logs" class="admin-tab">
  {% include 'components/admin/status-logs.html' %}
</div>

<!-- Settings Tab -->
<div id="tab-settings" class="admin-tab space-y-5">
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
    <div class="border-b border-gray-200 dark:border-gray-700 px-5 py-3">
      <h2 class="text-lg font-medium text-gray-800 dark:text-white flex items-center">
        <i class="bi bi-sliders mr-2 text-blue-600 dark:text-blue-400"></i> Cài đặt hệ thống
      </h2>
    </div>
    <div class="p-5">
      <div class="space-y-4">
        <div>
          <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Giao diện</h3>
          <div class="flex items-center">
            <label for="darkModeToggle" class="flex items-center cursor-pointer">
              <div class="relative">
                <input type="checkbox" id="darkModeToggle" class="sr-only" onchange="toggleThemeFromSwitch()">
                <div class="block bg-gray-300 dark:bg-gray-600 w-14 h-8 rounded-full"></div>
                <div class="dot absolute left-1 top-1 bg-white w-6 h-6 rounded-full transition"></div>
              </div>
              <div class="ml-3 text-gray-700 dark:text-gray-300 font-medium">
                Dark Mode
              </div>
            </label>
          </div>
        </div>
        
        <div class="pt-4 border-t border-gray-200 dark:border-gray-700">
          <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Thông tin hệ thống</h3>
          <div class="bg-gray-50 dark:bg-gray-700/50 p-4 rounded-md">
            <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
              <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Phiên bản</dt>
                <dd class="mt-1 text-sm text-gray-900 dark:text-white">1.0.0</dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Môi trường</dt>
                <dd class="mt-1 text-sm text-gray-900 dark:text-white">Production</dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Trạng thái dịch vụ</dt>
                <dd class="mt-1 text-sm text-green-600 dark:text-green-400 font-semibold">Hoạt động</dd>
              </div>
            </dl>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Set up dark mode toggle in settings
    const darkModeToggle = document.getElementById('darkModeToggle');
    
    // Initial state
    const currentTheme = localStorage.getItem('theme') || 'light';
    darkModeToggle.checked = currentTheme === 'dark';
    
    // Update dot position
    updateDarkModeToggleStyle();
    
    // Update dashboard initially
    updateDashboard();
  });
  
  function toggleThemeFromSwitch() {
    toggleTheme();
    updateDarkModeToggleStyle();
  }
  
  function updateDarkModeToggleStyle() {
    const darkModeToggle = document.getElementById('darkModeToggle');
    const dot = document.querySelector('.dot');
    
    if (darkModeToggle.checked) {
      dot.classList.add('translate-x-6');
    } else {
      dot.classList.remove('translate-x-6');
    }
  }
  
  function updateDashboard() {
    // Update last activity time
    document.getElementById('lastActivityTime').textContent = new Date().toLocaleString();
    
    // Get profile count if available
    const profileList = document.getElementById('profileList');
    if (profileList && profileList.querySelector('table')) {
      const rows = profileList.querySelectorAll('tbody tr');
      document.getElementById('dashProfileCount').textContent = rows.length;
    }
  }
  
  // Hook into tab switching
  const originalSwitchTab = window.switchTab;
  window.switchTab = function(tabId) {
    originalSwitchTab(tabId);
    
    if (tabId === 'dashboard') {
      updateDashboard();
    }
  };
</script>
{% endblock %}
