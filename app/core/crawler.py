import os
import asyncio
import platform
import subprocess
from pathlib import Path
from typing import Optional, Dict, Any, Callable, Awaitable
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON><PERSON>er<PERSON>ontex<PERSON>, Page
from . import selectors as sel
from .config import settings
from .message_classifier import ManusMessageClassifier
from urllib.parse import urlparse
from app.utils.download_file import handle_download
import time
import shutil
import logging

# Thêm biến global lưu trạng thái khởi tạo
_browser_initialized = False

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger("chrome_profile_setup")

async def ensure_browser_works():
    """
    Kiểm tra và đảm bảo trình duyệt hoạt động đúng với môi trường hiện tại.
    Điều này giúp phát hiện sớm các vấn đề với browser display.
    """
    global _browser_initialized
    if _browser_initialized:
        return True

    try:
        # Kiểm tra biến môi trường DISPLAY
        display_env = os.environ.get('DISPLAY', ':99')
        print(f">>> Current DISPLAY: {display_env}")

        # Kiểm tra Xvfb đang chạy
        try:
            subprocess.run(["xdpyinfo"], check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            print(">>> Xvfb seems to be running correctly")
        except Exception as e:
            print(f">>> WARNING: Xvfb might not be running properly: {str(e)}")

        # Khởi tạo Playwright và mở một browser đơn giản
        print(">>> Starting Playwright...")
        playwright = await async_playwright().start()

        # Sử dụng executable_path để chắc chắn tìm thấy đúng browser
        chromium_path = playwright.chromium.executable_path
        print(f">>> Found Chromium at: {chromium_path}")

        # Dùng thư mục tạm mới mỗi lần
        test_profile_path = os.path.join(settings.CHROME_PROFILE_BASE_PATH, f"_test_profile_{int(time.time())}")
        os.makedirs(test_profile_path, exist_ok=True)

        print(">>> Launching browser...")
        browser = await playwright.chromium.launch(
            headless=True,
            args=[
                "--no-sandbox",
                "--disable-setuid-sandbox",
                "--disable-dev-shm-usage",
                f"--display={display_env}",
            ]
        )

        # Chỉ tạo một context mới thay vì dùng profile
        context = await browser.new_context()
        page = await context.new_page()

        print(">>> Getting default page...")
        await page.goto("about:blank")

        # Không cần chụp ảnh để test
        print(">>> Browser working correctly")

        # Đóng browser
        await context.close()
        await browser.close()
        await playwright.stop()

        # Dọn dẹp thư mục tạm
        shutil.rmtree(test_profile_path, ignore_errors=True)

        # Đánh dấu đã khởi tạo thành công
        _browser_initialized = True
        print(">>> Browser test completed successfully!")
        return True
    except Exception as e:
        print(f">>> ERROR testing browser: {str(e)}")
        if 'browser' in locals():
            print("Browser logs:")
            print(browser.browser_type.playwright.devices)  # In thông tin gỡ lỗi
        return False
from app.utils.s3_upload import handle_download_s3
from app.utils.crawl_status_manager import crawl_status_manager, CrawlStatus

def get_system_chrome_user_data_dir() -> Optional[str]:
    """Lấy đường dẫn User Data Directory của Chrome hệ thống."""
    system = platform.system()
    home = Path.home()

    if system == "Windows":
        return str(home / "AppData" / "Local" / "Google" / "Chrome" / "User Data")
    elif system == "Darwin":  # macOS
        return str(home / "Library" / "Application Support" / "Google" / "Chrome")
    elif system == "Linux":
        return str(home / ".config" / "google-chrome")
    else:
        return None

def cleanup_profile_locks(user_data_dir: str):
    """Xóa các lock files để tránh lỗi SingletonLock."""
    if user_data_dir and os.path.exists(user_data_dir):
        lock_files = [
            os.path.join(user_data_dir, "SingletonLock"),
            os.path.join(user_data_dir, "lockfile"),
            os.path.join(user_data_dir, "SingletonSocket"),
            os.path.join(user_data_dir, "SingletonCookie")
        ]

        for lock_file in lock_files:
            try:
                if os.path.exists(lock_file):
                    os.remove(lock_file)
                    print(f"Removed lock file: {lock_file}")
            except Exception as e:
                print(f"Failed to remove lock file {lock_file}: {str(e)}")

        # Kiểm tra và xóa các file Network Persistent State
        network_state_file = os.path.join(user_data_dir, "Network", "Network Persistent State")
        try:
            if os.path.exists(network_state_file):
                os.remove(network_state_file)
                print(f"Removed network state file: {network_state_file}")
        except Exception as e:
            print(f"Failed to remove network state file: {str(e)}")

def kill_chrome_processes_for_profile(profile_name: str):
    """Kill Chrome processes sử dụng profile cụ thể."""
    import subprocess

    try:
        # Kill Chrome processes với profile name
        subprocess.run([
            "pkill", "-f", f"chrome.*{profile_name}"
        ], capture_output=True)

        subprocess.run([
            "pkill", "-f", f"Chromium.*{profile_name}"
        ], capture_output=True)

        # Đợi một chút để processes terminate
        import time
        time.sleep(0.5)

    except Exception:
        pass  # Ignore errors when killing processes

async def launch_browser_with_profile_safe(
    profile_name: Optional[str] = None,
    use_system_profile: bool = False,
    headless: bool = True,
    preserve_session: bool = True
) -> tuple[Browser, BrowserContext]:
    """
    Khởi chạy browser với profile được chỉ định, tối ưu cho việc preserve session.

    Args:
        profile_name: Tên profile tùy chỉnh
        use_system_profile: Sử dụng profile Chrome hệ thống
        headless: Chạy ở chế độ headless
        preserve_session: Tối ưu để giữ session khi chạy headless

    Returns:
        Tuple của (playwright, context)
    """
    playwright = await async_playwright().start()

    if use_system_profile:
        user_data_dir = get_system_chrome_user_data_dir()
        if not user_data_dir or not os.path.exists(user_data_dir):
            raise ValueError("Không tìm thấy Chrome profile hệ thống")
    elif profile_name:
        user_data_dir = os.path.join(settings.CHROME_PROFILE_BASE_PATH, profile_name)
        os.makedirs(user_data_dir, exist_ok=True)
    else:
        user_data_dir = None

    # Cleanup lock files và kill processes trước khi launch
    if user_data_dir and profile_name:
        kill_chrome_processes_for_profile(profile_name)
        cleanup_profile_locks(user_data_dir)

    try:
        # Tạo một user agent string riêng cho mỗi profile để tránh xung đột
        custom_user_agent = f"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36 Profile/{profile_name}"

        browser = await playwright.chromium.launch_persistent_context(
            user_data_dir=user_data_dir,
            headless=headless,
            viewport=None,  # Bỏ viewport để tránh xung đột
            ignore_default_args=["--enable-automation"],
            chromium_sandbox=False,
            args=[
                "--no-sandbox",
                "--disable-setuid-sandbox",
                "--disable-dev-shm-usage",
                f"--display={os.environ.get('DISPLAY', ':99')}",
                "--disable-blink-features=AutomationControlled",
                "--window-position=0,0",  # Đặt vị trí cửa sổ
                "--window-size=1600,900",
                "--no-first-run",
                "--start-maximized",
                "--disable-extensions"
            ]
        )
    except Exception as e:
        # Nếu vẫn lỗi, thử cleanup lần nữa và retry
        if "SingletonLock" in str(e) or "ProcessSingleton" in str(e):
            if user_data_dir and profile_name:
                kill_chrome_processes_for_profile(profile_name)
                cleanup_profile_locks(user_data_dir)
                # Đợi một chút rồi thử lại
                await asyncio.sleep(2)
                browser = await playwright.chromium.launch_persistent_context(
                    user_data_dir=user_data_dir,
                    headless=headless,
                    args=[
                        "--no-sandbox",
                        "--disable-blink-features=AutomationControlled",
                        "--disable-web-security",
                        "--disable-features=VizDisplayCompositor",
                        "--disable-dev-shm-usage",
                        "--no-first-run",
                        "--disable-default-apps",
                        "--start-maximized",
                        "--window-size=1920,1080",
                        "--force-device-scale-factor=1",
                        "--enable-features=ChromeWhatsNew,DesktopPWAsRunOnOsLogin"
                    ]
                )
            else:
                raise e
        else:
            raise e

    return playwright, browser

async def setup_chrome_profile_interactive(
    profile_name: str,
    url: str = "https://google.com/",
    websocket_callback: Optional[Callable[[str, str], Awaitable[None]]] = None,
    request_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Thiết lập Chrome profile bằng cách mở trình duyệt non-headless
    để người dùng đăng nhập thủ công qua noVNC.

    Args:
        profile_name: Tên profile để tạo/sử dụng
        url: URL để mở ban đầu (mặc định là Google.com)
        websocket_callback: Callback để gửi cập nhật realtime
        request_id: ID của request để tracking

    Returns:
        Dictionary chứa thông tin về việc setup profile
    """
    global active_browser_for_profiles
    if 'active_browser_for_profiles' not in globals():
        active_browser_for_profiles = {}

    async def send_progress(message: str):
        """Gửi thông báo tiến trình qua WebSocket nếu có callback."""
        if websocket_callback and request_id:
            await websocket_callback(request_id, message)

    try:
        logger.debug(f"[CHROME_SETUP] Bắt đầu thiết lập profile: {profile_name}")
        # Thông báo bắt đầu quá trình
        await send_progress(f"Đang khởi tạo profile '{profile_name}'...")

        # Tạo thư mục profile nếu chưa tồn tại
        profile_path = os.path.join(settings.CHROME_PROFILE_BASE_PATH, profile_name)
        os.makedirs(profile_path, exist_ok=True)

        # Cleanup lock files và kill processes trước khi launch
        kill_chrome_processes_for_profile(profile_name)
        cleanup_profile_locks(profile_path)

        await send_progress(f"Đang khởi chạy trình duyệt với profile '{profile_name}'...")

        playwright = await async_playwright().start()

        # Đảm bảo chúng ta có thể hiển thị trình duyệt trong Xvfb
        await send_progress(f"Đang chuẩn bị Chromium cho profile '{profile_name}'...")

        # Sử dụng executable_path để chắc chắn tìm thấy đúng browser
        chromium_path = playwright.chromium.executable_path
        await send_progress(f"Tìm thấy Chromium tại: {chromium_path}")

        display_env = os.environ.get('DISPLAY', ':99')
        logger.debug(f"[CHROME_SETUP] DISPLAY={display_env}")

        # Thiết lập lại lệnh khởi chạy để tương thích với Xvfb/noVNC trong Docker
        browser = await playwright.chromium.launch_persistent_context(
            user_data_dir=profile_path,
            headless=False,
            viewport=None,  # Bỏ viewport để tránh xung đột
            ignore_default_args=["--enable-automation"],
            chromium_sandbox=False,
            args=[
                "--no-sandbox",
                "--disable-setuid-sandbox",
                "--disable-dev-shm-usage",
                f"--display={display_env}",
                "--disable-blink-features=AutomationControlled",
                "--window-position=0,0",  # Đặt vị trí cửa sổ
                "--window-size=1600,900",
                "--no-first-run",
                "--start-maximized",
                "--disable-extensions"
            ]
        )

        # Lưu trữ browser context để có thể đóng sau khi hoàn thành
        active_browser_for_profiles[profile_name] = (playwright, browser)

        # Mở một trang mới
        page = await browser.new_page()
        await send_progress(f"Đang mở trang {url}...")

        # Navigate đến URL được chỉ định
        await page.goto(url)


        # Thêm đoạn JS để đảm bảo Chrome hiển thị đúng trong noVNC
        await page.evaluate("""() => {
            window.moveTo(0, 0);
            window.resizeTo(screen.width, screen.height);
        }""")

        # Kích hoạt cửa sổ
        await page.evaluate("""() => {
            window.focus();
            window.moveTo(0, 0);
            window.resizeTo(screen.width, screen.height);
        }""")

        # Gọi xwindow API để đảm bảo cửa sổ hiển thị
        import subprocess
        subprocess.run(["DISPLAY=:99", "wmctrl", "-a", "Google"], shell=True)
        await send_progress(f"Trình duyệt đã khởi chạy. Bạn có thể tương tác với Chrome qua noVNC.")

        return {
            "success": True,
            "profile_name": profile_name,
            "status": "running",
            "message": f"Profile '{profile_name}' đang được thiết lập. Sử dụng noVNC để tương tác."
        }

    except Exception as e:
        await send_progress(f"Lỗi: {str(e)}")
        return {
            "success": False,
            "profile_name": profile_name,
            "error": str(e),
            "message": f"Lỗi khi thiết lập profile '{profile_name}': {str(e)}"
        }

async def complete_profile_setup(
    profile_name: str,
    websocket_callback: Optional[Callable[[str, str], Awaitable[None]]] = None,
    request_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Hoàn thành quá trình thiết lập profile bằng cách đóng trình duyệt.

    Args:
        profile_name: Tên profile đang được thiết lập
        websocket_callback: Callback để gửi cập nhật realtime
        request_id: ID của request để tracking

    Returns:
        Dictionary chứa thông tin kết quả
    """
    global active_browser_for_profiles
    if 'active_browser_for_profiles' not in globals():
        active_browser_for_profiles = {}

    async def send_progress(message: str):
        """Gửi thông báo tiến trình qua WebSocket nếu có callback."""
        if websocket_callback and request_id:
            await websocket_callback(request_id, message)

    try:
        await send_progress(f"Đang hoàn thành thiết lập profile '{profile_name}'...")

        # Kiểm tra xem có browser instance cho profile này không
        if profile_name in active_browser_for_profiles:
            playwright, browser = active_browser_for_profiles[profile_name]

            # Đóng browser context
            await send_progress(f"Đang đóng trình duyệt để lưu profile '{profile_name}'...")
            try:
                # Hiển thị thông báo đóng
                if len(browser.pages) > 0:
                    page = browser.pages[0]
                    await page.evaluate("""() => {
                        const div = document.createElement('div');
                        div.style.position = 'fixed';
                        div.style.top = '0';
                        div.style.left = '0';
                        div.style.width = '100%';
                        div.style.height = '100%';
                        div.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
                        div.style.color = 'white';
                        div.style.display = 'flex';
                        div.style.justifyContent = 'center';
                        div.style.alignItems = 'center';
                        div.style.fontSize = '24px';
                        div.style.fontFamily = 'Arial, sans-serif';
                        div.style.zIndex = '999999';
                        div.innerHTML = '<div style="text-align:center"><h1>Đang lưu Profile</h1><p>Vui lòng đợi...</p></div>';
                        document.body.appendChild(div);
                    }""")
                    await asyncio.sleep(1)  # Đợi thông báo hiển thị
            except:
                pass

            # Đóng browser
            await browser.close()
            await playwright.stop()

            # Xóa khỏi dict
            del active_browser_for_profiles[profile_name]

            await send_progress(f"Đã lưu profile '{profile_name}' thành công.")

            # Kill Chrome processes nếu còn để đảm bảo cleanup hoàn toàn
            kill_chrome_processes_for_profile(profile_name)

            return {
                "success": True,
                "profile_name": profile_name,
                "status": "completed",
                "message": f"Thiết lập profile '{profile_name}' đã hoàn tất thành công."
            }
        else:
            # Trường hợp không tìm thấy browser instance
            await send_progress(f"Không tìm thấy phiên trình duyệt cho profile '{profile_name}'. Có thể đã đóng hoặc chưa mở.")

            # Thử cleanup để đảm bảo không còn process nào
            profile_path = os.path.join(settings.CHROME_PROFILE_BASE_PATH, profile_name)
            kill_chrome_processes_for_profile(profile_name)
            cleanup_profile_locks(profile_path)

            return {
                "success": True,
                "profile_name": profile_name,
                "status": "completed",
                "message": f"Profile '{profile_name}' đã được hoàn thành (không có phiên trình duyệt cần đóng)."
            }

    except Exception as e:
        await send_progress(f"Lỗi khi hoàn thành profile: {str(e)}")
        return {
            "success": False,
            "profile_name": profile_name,
            "error": str(e),
            "message": f"Lỗi khi hoàn thành thiết lập profile '{profile_name}': {str(e)}"
        }

async def crawl_manus_page_content(
    html_content: Optional[str] = None,
    url: Optional[str] = None,
    profile_name: Optional[str] = None,
    use_system_profile: bool = False,
    headless: bool = False,
    websocket_callback: Optional[Callable[[str, str], Awaitable[None]]] = None,
    request_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Crawl nội dung từ trang Manus.im hoặc parse HTML tĩnh.

    Args:
        html_content: Nội dung HTML tĩnh để parse
        url: URL để crawl (nếu không có html_content)
        profile_name: Tên profile Chrome để sử dụng
        use_system_profile: Sử dụng profile Chrome hệ thống
        headless: Chạy ở chế độ headless
        websocket_callback: Callback để gửi cập nhật realtime
        request_id: ID của request để tracking

    Returns:
        Dictionary chứa dữ liệu đã crawl
    """

    async def send_progress(message: str):
        """Gửi thông báo tiến trình qua WebSocket nếu có callback."""
        if websocket_callback and request_id:
            await websocket_callback(request_id, message)

    task_id = None
    try:
        # Tạo task_id từ URL hoặc request_id
        if url:
            task_id = extract_task_id(url)
        else:
            task_id = request_id or "static_html"

        # Tạo task trong status manager
        if task_id:
            await crawl_status_manager.create_task(task_id, {
                "url": url,
                "profile_name": profile_name,
                "headless": headless
            })
            await crawl_status_manager.update_status(task_id, CrawlStatus.IN_PROGRESS)

        await send_progress("Bắt đầu khởi chạy trình duyệt...")

        playwright, context = await launch_browser_with_profile_safe(
            profile_name=profile_name,
            # profile_name="manus_login_profile",
            use_system_profile=use_system_profile,
            headless=headless
        )

        page = await context.new_page()

        if html_content:
            await send_progress("Đang load HTML content...")
            await page.set_content(html_content)
        elif url:
            await send_progress(f"Đang truy cập URL: {url}")
            await page.goto(url, wait_until="networkidle")
        else:
            raise ValueError("Cần cung cấp html_content hoặc url")

        await send_progress("Đang trích xuất dữ liệu...")

        # Trích xuất dữ liệu theo selectors
        data = {
            "page_title": "",
            # "tasks": [],
            "current_task_title": "",
            "chat_messages": [],
            # "footer_user": {},
            # "profile_status": {
            #     "profile_name": profile_name,
            #     "use_system_profile": use_system_profile,
            #     "headless": headless
            # }
            "downloaded_files": [],
            "task_id": task_id  # Thêm task_id vào response
        }

        # Lấy title trang
        try:
            data["page_title"] = await page.title()
        except:
            pass

        # Lấy danh sách tasks từ sidebar
        await send_progress("Đang lấy danh sách tasks...")
        try:
            task_containers = await page.query_selector_all(sel.TASK_ITEM_CONTAINER_CSS)
            for container in task_containers:
                task_data = {}

                # Icon
                try:
                    icon_img = await container.query_selector(sel.TASK_ITEM_ICON_IMG_CSS)
                    if icon_img:
                        task_data["icon_src"] = await icon_img.get_attribute("src")
                except:
                    pass

                # Title
                try:
                    title_span = await container.query_selector(sel.TASK_ITEM_TITLE_SPAN_CSS)
                    if title_span:
                        task_data["title"] = await title_span.get_attribute("title")
                        task_data["title_text"] = await title_span.inner_text()
                except:
                    pass

                # Timestamp
                try:
                    timestamp_span = await container.query_selector(sel.TASK_ITEM_TIMESTAMP_CSS)
                    if timestamp_span:
                        task_data["timestamp"] = await timestamp_span.inner_text()
                except:
                    pass

                # Preview
                try:
                    preview_span = await container.query_selector(sel.TASK_ITEM_PREVIEW_SPAN_CSS)
                    if preview_span:
                        task_data["preview"] = await preview_span.get_attribute("title")
                except:
                    pass

                # if task_data:
                #     data["tasks"].append(task_data)
        except Exception as e:
            await send_progress(f"Lỗi khi lấy tasks: {str(e)}")

        # Lấy tiêu đề task hiện tại
        try:
            current_title_elem = await page.query_selector(sel.CURRENT_TASK_TITLE_MAIN_CSS)
            if current_title_elem:
                data["current_task_title"] = await current_title_elem.inner_text()
        except:
            pass

        # Lấy tin nhắn chat
        await send_progress("Đang lấy tin nhắn chat...")
        try:
            chat_events = await page.query_selector_all(sel.CHAT_EVENT_CONTAINER_CSS)
            for event in chat_events:
                message_data = {
                    "event_id": await event.get_attribute("data-event-id")
                }

                # Tin nhắn user
                try:
                    user_msg = await event.query_selector(sel.USER_MESSAGE_TEXT_CSS)
                    if user_msg:
                        message_data["user_message"] = await user_msg.inner_text()
                        message_data["type"] = "user"
                except:
                    pass

                # Tin nhắn Manus
                try:
                    manus_msg = await event.query_selector(sel.MANUS_MESSAGE_CONTENT_PROSE_CSS)
                    if manus_msg:
                        manus_text = await manus_msg.inner_text()
                        manus_html = await manus_msg.inner_html()

                        message_data["manus_message"] = manus_text
                        # message_data["manus_html"] = manus_html
                        message_data["type"] = "manus"

                        # Phân loại message type cho Manus
                        try:
                            classification = ManusMessageClassifier.classify_message_type(
                                manus_html=manus_html,
                                manus_text=manus_text
                            )
                            message_data["message_subtype"] = classification.get("message_subtype", "text")
                            # message_data["content_analysis"] = classification.get("content_analysis", {})
                        except Exception as classify_error:
                            # Fallback nếu phân loại thất bại
                            message_data["message_subtype"] = "text"
                            message_data["content_analysis"] = {"error": str(classify_error)}
                except:
                    pass

                # Timestamp
                try:
                    timestamp_elem = await event.query_selector(sel.MESSAGE_TIMESTAMP_CSS)
                    if timestamp_elem:
                        message_data["timestamp"] = await timestamp_elem.inner_text()
                except:
                    pass

                # Attachments - Cập nhật để upload lên Cloudinary
                try:
                    button_view_all_file = await event.query_selector(sel.VIEW_ALL_FILE_IN_MESSAGE_CSS)
                    # Line 368
                    await send_progress("Đang tìm nút 'View all files in message'...") # Added await
                    await asyncio.sleep(2)
                    # Line 370
                    if button_view_all_file:
                        # Update status to uploading
                        if task_id:
                            await crawl_status_manager.update_status(task_id, CrawlStatus.UPLOADING)

                        # Thiết lập lắng nghe sự kiện download trước khi click
                        download_promise = page.wait_for_event('download')

                        await button_view_all_file.click()
                        await send_progress("Clicked 'View all files in message' button") # Ensure this is also awaited if not already
                        await asyncio.sleep(2)

                        download_button = await page.query_selector(sel.DOWNLOAD_BUTTON_CSS)
                        if download_button:
                            await download_button.click()
                            await send_progress("Clicked download button") # Ensure this is also awaited if not already
                            await asyncio.sleep(2)

                            # Xử lý batch download
                            batch_download_button = await page.query_selector(sel.BATCH_DOWNLOAD_BUTTON_CSS)
                            if batch_download_button:
                                # Thiết lập callback cho tất cả các download tiếp theo
                                downloaded_files = []

                                async def on_download(download):
                                    # Sử dụng handle_download_cloudinary thay vì handle_download
                                    download_result = await handle_download_s3(download, task_id)
                                    if download_result:
                                        # Xử lý theo loại download result
                                        download_type = download_result.get("type", "unknown")

                                        if download_type == "zip_extracted":
                                            # File zip đã được giải nén và upload từng file
                                            zip_filename = download_result.get("zip_filename")
                                            extracted_files = download_result.get("extracted_files", [])
                                            total_extracted = download_result.get("total_extracted", 0)

                                            await send_progress(f"📦 Giải nén ZIP: {zip_filename} -> {total_extracted} files")

                                            # Thêm từng file extracted vào status manager
                                            for extracted_file in extracted_files:
                                                downloaded_files.append(extracted_file)
                                                if task_id:
                                                    await crawl_status_manager.add_uploaded_file(task_id, extracted_file)

                                            await send_progress(f"✅ ZIP processed: {total_extracted} files uploaded from {zip_filename}")

                                            # Thêm thông tin summary về zip extraction
                                            zip_summary = {
                                                "type": "zip_summary",
                                                "zip_filename": zip_filename,
                                                "total_extracted": total_extracted,
                                                "extracted_files": extracted_files,
                                                "local_path": download_result.get("local_path")
                                            }
                                            downloaded_files.append(zip_summary)

                                        elif download_type == "zip_original":
                                            # Zip upload nguyên bản (giải nén thất bại)
                                            downloaded_files.append(download_result)
                                            if task_id:
                                                await crawl_status_manager.add_uploaded_file(task_id, download_result)

                                            filename = download_result.get('filename', 'unknown')
                                            s3_url = download_result.get('s3_url', 'N/A')
                                            await send_progress(f"⚠️ ZIP upload as original: {filename} -> {s3_url} (extraction failed)")

                                        elif download_type == "regular_file":
                                            # File thường
                                            downloaded_files.append(download_result)
                                            if task_id:
                                                await crawl_status_manager.add_uploaded_file(task_id, download_result)

                                            filename = download_result.get('filename', 'unknown')
                                            s3_url = download_result.get('s3_url', 'N/A')
                                            await send_progress(f"✅ File uploaded: {filename} -> {s3_url}")

                                        elif download_type == "upload_failed":
                                            # Upload thất bại nhưng vẫn lưu thông tin
                                            downloaded_files.append(download_result)
                                            filename = download_result.get('filename', 'unknown')
                                            await send_progress(f"❌ Upload failed: {filename}")

                                        elif download_type == "error":
                                            # Lỗi download
                                            filename = download_result.get('filename', 'unknown')
                                            error = download_result.get('error', 'Unknown error')
                                            await send_progress(f"❌ Download error: {filename} - {error}")
                                        else:
                                            # Fallback cho type không xác định
                                            downloaded_files.append(download_result)
                                            filename = download_result.get('filename', 'unknown')
                                            await send_progress(f"✅ File processed: {filename}")
                                    else:
                                        await send_progress("❌ Download processing failed")

                                page.on('download', on_download) # Note: page.on does not return an awaitable

                                await batch_download_button.click()
                                await send_progress("Đang tải và upload files...")
                                await asyncio.sleep(5)  # Đợi lâu hơn cho batch download

                                if downloaded_files:
                                    message_data["cloudinary_files"] = downloaded_files
                                    data["downloaded_files"].extend(downloaded_files)

                except Exception as e: # It's good practice to log the exception or be more specific
                    await send_progress(f"Lỗi khi xử lý attachments: {str(e)}") # Added await here too
                    pass # Avoid bare excepts if possible

                if "user_message" in message_data or "manus_message" in message_data:
                    data["chat_messages"].append(message_data)
        except Exception as e:
            await send_progress(f"Lỗi khi lấy chat messages: {str(e)}")

        # # Lấy thông tin user từ footer
        # await send_progress("Đang lấy thông tin user...")
        # try:
        #     footer_avatar = await page.query_selector(sel.USER_AVATAR_IMG_CSS)
        #     if footer_avatar:
        #         data["footer_user"]["avatar_src"] = await footer_avatar.get_attribute("src")
        # except:
        #     pass

        # try:
        #     footer_name = await page.query_selector(sel.USER_NAME_SPAN_CSS)
        #     if footer_name:
        #         data["footer_user"]["name"] = await footer_name.inner_text()
        # except:
        #     pass

        await context.close()
        await playwright.stop()

        await send_progress("Hoàn thành crawl dữ liệu!")

        # Cập nhật status thành completed
        if task_id:
            await crawl_status_manager.update_status(task_id, CrawlStatus.COMPLETED)

        return {
            "success": True,
            "data": data,
            "message": "Crawl thành công",
            "task_id": task_id,
            "total_files": len(data["downloaded_files"])
        }

    except Exception as e:
        error_msg = f"Lỗi khi crawl: {str(e)}"
        await send_progress(error_msg)

        # Cập nhật status thành error
        if task_id:
            await crawl_status_manager.update_status(task_id, CrawlStatus.ERROR, str(e))

        return {
            "success": False,
            "error": str(e),
            "message": error_msg,
            "task_id": task_id
        }

async def chat_with_manus_interactive(
    message: str,
    task_url: str,
    profile_name: Optional[str] = None,
    headless: bool = True,
    websocket_callback: Optional[Callable[[str, str], Awaitable[None]]] = None,
    request_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Gửi message lên Manus.im và crawl response realtime

    Args:
        message: Message to send to Manus
        task_url: Manus task URL (e.g., https://manus.im/app/task-id)
        profile_name: Chrome profile name for authentication
        headless: Run in headless mode
        websocket_callback: Callback for realtime updates
        request_id: Request ID for WebSocket tracking

    Returns:
        Dict containing chat response and updated page data
    """

    async def send_progress(msg: str):
        """Gửi thông báo tiến trình qua WebSocket nếu có callback."""
        if websocket_callback and request_id:
            await websocket_callback(request_id, f"💬 {msg}")

    try:
        await send_progress("Initializing chat session...")

        # Backup session và extract cookies nếu đang dùng headless
        if headless and profile_name:
            await send_progress("Backing up session data...")
            # await backup_session_data(profile_name)

            await send_progress("Extracting cookies for injection...")
            await extract_cookies_from_profile(profile_name)

        # Launch browser với mode phù hợp
        if headless:
            await send_progress("Starting browser with stealth headless mode...")
            playwright, context = await launch_browser_with_stealth_mode(
                profile_name=profile_name,
                force_session_preservation=True
            )

            # Inject cookies để maintain login state
            if profile_name:
                await send_progress("Injecting cookies to maintain login...")
                await inject_cookies_to_context(context, profile_name)
        else:
            await send_progress("Starting browser with standard mode...")
            playwright, context = await launch_browser_with_profile_safe(
                profile_name=profile_name,
                headless=headless,
                preserve_session=True
            )

        page = await context.new_page()

        # Navigate to task URL
        await send_progress(f"Navigating to task: {task_url}")
        await page.goto(task_url, wait_until='networkidle')

        # Wait for page to load
        await send_progress("Waiting for page to load...")
        await page.wait_for_load_state('networkidle')

        # Kiểm tra trạng thái login trước
        await send_progress("Checking login status...")
        is_logged_in = await check_login_status(page)

        if not is_logged_in:
            await send_progress("❌ Not logged in. Please setup Chrome profile first.")
            await context.close()
            await playwright.stop()
            return {
                "success": False,
                "error": "Not logged in. Please setup Chrome profile first.",
                "current_url": page.url,
                "suggestion": f"Please run setup profile for '{profile_name}' first"
            }

        # Find chat input
        await send_progress("Looking for chat input...")
        chat_input_selector = sel.CHAT_INPUT_TEXTAREA_CSS

        try:
            await page.wait_for_selector(chat_input_selector, timeout=10000)
        except:
            await send_progress("❌ Chat input not found - checking if logged in...")
            # Check if we need to login
            current_url = page.url
            if "login" in current_url or "auth" in current_url:
                await send_progress("❌ Not logged in. Please setup Chrome profile first.")
                await context.close()
                await playwright.stop()
                return {
                    "success": False,
                    "error": "Not logged in. Please setup Chrome profile first.",
                    "current_url": current_url
                }
            else:
                await send_progress("❌ Chat interface not available on this page")
                await context.close()
                await playwright.stop()
                return {
                    "success": False,
                    "error": "Chat interface not found on this page",
                    "current_url": current_url
                }

        # Get current messages count for comparison
        await send_progress("Counting existing messages...")
        existing_messages = await page.query_selector_all(sel.CHAT_EVENT_CONTAINER_CSS)
        initial_message_count = len(existing_messages)

        # Type and send message
        await send_progress(f"Sending message: '{message[:50]}{'...' if len(message) > 50 else ''}'")
        await page.fill(chat_input_selector, message)
        await page.press(chat_input_selector, 'Enter')

        # Wait for new message to appear
        await send_progress("Waiting for Manus response...")
        max_wait_time = 30  # 30 seconds timeout
        wait_interval = 1   # Check every 1 second
        waited_time = 0

        new_response = None

        # Lấy URL hiện tại và extract task ID
        current_url = page.url
        await send_progress(f"Current URL: {current_url}")
        extracted_task_id = extract_task_id(current_url)

        while waited_time < max_wait_time:
            await asyncio.sleep(wait_interval)
            waited_time += wait_interval

            # Check for new messages
            current_messages = await page.query_selector_all(sel.CHAT_EVENT_CONTAINER_CSS)
            current_count = len(current_messages)

            if current_count > initial_message_count:
                await send_progress(f"New message detected! ({current_count} vs {initial_message_count})")

                # Get the latest message
                latest_message = current_messages[-1]

                # Check if it's from Manus (not user)
                user_message_elements = await latest_message.query_selector_all(sel.USER_MESSAGE_TEXT_CSS)

                if not user_message_elements:  # No user message elements = Manus response
                    # Extract Manus response
                    manus_content_elements = await latest_message.query_selector_all(sel.MANUS_MESSAGE_CONTENT_PROSE_CSS)

                    if manus_content_elements:
                        manus_response = await manus_content_elements[0].inner_text()
                        await send_progress(f"✅ Got Manus response: '{manus_response[:100]}{'...' if len(manus_response) > 100 else ''}'")

                        # Check for attachments
                        attachments = []
                        attachment_elements = await latest_message.query_selector_all(sel.ATTACHMENT_IN_MESSAGE_CONTAINER_CSS)

                        for attachment in attachment_elements:
                            try:
                                filename_elem = await attachment.query_selector(sel.ATTACHMENT_IN_MESSAGE_FILENAME_CSS)
                                details_elem = await attachment.query_selector(sel.ATTACHMENT_IN_MESSAGE_DETAILS_CSS)

                                attachment_data = {}
                                if filename_elem:
                                    attachment_data["filename"] = await filename_elem.inner_text()
                                if details_elem:
                                    attachment_data["details"] = await details_elem.inner_text()

                                if attachment_data:
                                    attachments.append(attachment_data)


                            except:
                                pass

                        new_response = {
                            "user_message": message,
                            "manus_response": manus_response,
                            "timestamp": await get_current_timestamp(),
                            "attachments": attachments
                        }
                        break

            await send_progress(f"Still waiting... ({waited_time}/{max_wait_time}s)")

        if not new_response:
            await send_progress("⏰ Timeout waiting for Manus response")
            await context.close()
            await playwright.stop()
            return {
                "success": False,
                "error": "Timeout waiting for Manus response",
                "task_id": extracted_task_id,
                "timeout": max_wait_time
            }

        # Crawl updated page data
        await send_progress("Crawling updated page data...")
        updated_data = await crawl_page_data_internal(page)

        await page.wait_for_timeout(10000);

        # Kiểm tra lại trạng thái login sau khi chat
        await send_progress("Verifying login status after chat...")
        is_still_logged_in = await check_login_status(page)

        if not is_still_logged_in:
            await send_progress("⚠️ WARNING: Session may have been lost during chat!")



        await context.close()
        await playwright.stop()

        # Restore session nếu cần thiết
        if headless and profile_name:
            await send_progress("Restoring session data...")
            await restore_session_data(profile_name)

        await send_progress("✅ Interactive chat completed successfully!")

        return {
            "success": True,
            "chat_response": new_response,
            "updated_page_data": updated_data,
            "timestamp": await get_current_timestamp() ,
            "task_id": extracted_task_id,
            "session_status": {
                "initially_logged_in": True,
                "still_logged_in_after_chat": is_still_logged_in,
                "profile_used": profile_name,
                "headless_mode": headless,
                "stealth_mode_used": headless,
                "session_backup_restored": headless and profile_name
            }
        }

    except Exception as e:
        await send_progress(f"❌ Error: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "message": f"Error in interactive chat: {str(e)}"
        }

async def get_current_timestamp() -> str:
    """Get current timestamp in ISO format."""
    from datetime import datetime
    return datetime.now().isoformat()

async def crawl_page_data_internal(page) -> Dict[str, Any]:
    """Internal function to crawl page data from an existing page."""
    data = {
        "page_title": "",
        # "tasks": [],
        "current_task_title": "",
        "chat_messages": [],
        # "footer_user": {}
    }

    # Get page title
    try:
        data["page_title"] = await page.title()
    except:
        pass

    # Get tasks from sidebar
    try:
        task_containers = await page.query_selector_all(sel.TASK_ITEM_CONTAINER_CSS)
        for container in task_containers:
            task_data = {}

            # Icon
            try:
                icon_img = await container.query_selector(sel.TASK_ITEM_ICON_IMG_CSS)
                if icon_img:
                    task_data["icon_src"] = await icon_img.get_attribute("src")
            except:
                pass

            # Title
            try:
                title_span = await container.query_selector(sel.TASK_ITEM_TITLE_SPAN_CSS)
                if title_span:
                    task_data["title"] = await title_span.get_attribute("title")
                    task_data["title_text"] = await title_span.inner_text()
            except:
                pass

            # Timestamp
            try:
                timestamp_span = await container.query_selector(sel.TASK_ITEM_TIMESTAMP_CSS)
                if timestamp_span:
                    task_data["timestamp"] = await timestamp_span.inner_text()
            except:
                pass

            # Preview
            try:
                preview_span = await container.query_selector(sel.TASK_ITEM_PREVIEW_SPAN_CSS)
                if preview_span:
                    task_data["preview"] = await preview_span.get_attribute("title")
            except:
                pass

            # if task_data:
            #     data["tasks"].append(task_data)
    except:
        pass

    # Get current task title
    try:
        current_title_elem = await page.query_selector(sel.CURRENT_TASK_TITLE_MAIN_CSS)
        if current_title_elem:
            data["current_task_title"] = await current_title_elem.inner_text()
    except:
        pass

    # Get chat messages
    try:
        chat_events = await page.query_selector_all(sel.CHAT_EVENT_CONTAINER_CSS)
        for event in chat_events:
            message_data = {
                "event_id": await event.get_attribute("data-event-id")
            }

            # User message
            try:
                user_msg = await event.query_selector(sel.USER_MESSAGE_TEXT_CSS)
                if user_msg:
                    message_data["user_message"] = await user_msg.inner_text()
                    message_data["type"] = "user"
            except:
                pass

            # Manus message
            try:
                manus_msg = await event.query_selector(sel.MANUS_MESSAGE_CONTENT_PROSE_CSS)
                if manus_msg:
                    manus_text = await manus_msg.inner_text()
                    manus_html = await manus_msg.inner_html()

                    message_data["manus_message"] = manus_text
                    # message_data["manus_html"] = manus_html
                    message_data["type"] = "manus"

                    # Message classification
                    try:
                        classification = ManusMessageClassifier.classify_message_type(
                            manus_html=manus_html,
                            manus_text=manus_text
                        )
                        message_data["message_subtype"] = classification.get("message_subtype", "text")
                    except:
                        message_data["message_subtype"] = "text"
            except:
                pass

            # Timestamp
            try:
                timestamp_elem = await event.query_selector(sel.MESSAGE_TIMESTAMP_CSS)
                if timestamp_elem:
                    message_data["timestamp"] = await timestamp_elem.inner_text()
            except:
                pass

            if "user_message" in message_data or "manus_message" in message_data:
                data["chat_messages"].append(message_data)
    except:
        pass

    # Get footer user info
    try:
        footer_avatar = await page.query_selector(sel.FOOTER_USER_AVATAR_IMG_CSS)
        footer_name = await page.query_selector(sel.FOOTER_USER_NAME_SPAN_CSS)

        if footer_avatar or footer_name:
            data["footer_user"] = {
                "avatar_src": await footer_avatar.get_attribute("src") if footer_avatar else None,
                "name": await footer_name.inner_text() if footer_name else None
            }
    except:
        pass

    return data

def extract_task_id(url: str) -> str:
    """Trích xuất taskId từ URL Manus."""
    parsed_url = urlparse(url)
    path_parts = parsed_url.path.strip('/').split('/')
    if len(path_parts) >= 2 and path_parts[0] == 'app':
        return path_parts[1]
    return None

async def check_status_by_task(
    task_id: str,
    profile_name: Optional[str] = None,
    headless: bool = True,
    websocket_callback: Optional[Callable[[str, str], Awaitable[None]]] = None,
    request_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Gửi message lên Manus.im và crawl response realtime

    Args:
        message: Message to send to Manus
        task_url: Manus task URL (e.g., https://manus.im/app/task-id)
        profile_name: Chrome profile name for authentication
        headless: Run in headless mode
        websocket_callback: Callback for realtime updates
        request_id: Request ID for WebSocket tracking

    Returns:
        Dict containing chat response and updated page data
    """

    async def send_progress(msg: str):
        """Gửi thông báo tiến trình qua WebSocket nếu có callback."""
        if websocket_callback and request_id:
            await websocket_callback(request_id, f"💬 {msg}")

    try:
        await send_progress("Initializing chat session...")

        # Launch browser with profile
        await send_progress("Starting browser with profile...")
        playwright, context = await launch_browser_with_profile_safe(
            profile_name=profile_name,
            headless=headless
        )

        page = await context.new_page()

        task_url = 'https://manus.im/app/' + task_id
        # Navigate to task URL
        await send_progress(f"Navigating to task: {task_url}")
        await page.goto(task_url, wait_until='networkidle')

        # Wait for page to load
        await send_progress("Waiting for page to load...")
        await page.wait_for_load_state('networkidle')

        check_status = page.locator(sel.MAKE_COMPLETED_CSS)
        completed_count = await check_status.count()

        copy_button = page.locator(sel.COPY_BUTTON_CSS)
        copy_button_count = await copy_button.count()

        await context.close()
        await playwright.stop()

        # Kiểm tra cả 2 trường hợp
        if completed_count > 0:
            await send_progress("Task completed (detected completion message)")
            return {
                "success": True,
                "message": "Task completed - completion message found",
                "status": "completed",
                "detection_method": "completion_message"
            }
        elif copy_button_count > 0:
            await send_progress("Task completed (detected copy button)")
            return {
                "success": True,
                "message": "Task completed - copy button found",
                "status": "completed",
                "detection_method": "copy_button"
            }
        else:
            await send_progress("Task not completed")
            return {
                "success": True,
                "message": "Task not completed",
                "status": "not_completed",
                "detection_method": "none"
            }

    except Exception as e:
        await send_progress(f"❌ Error: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "message": f"Error in interactive chat: {str(e)}",
            "status": "error"
        }

async def check_login_status(page) -> bool:
    """
    Kiểm tra xem user đã login vào Manus hay chưa.

    Args:
        page: Playwright page object

    Returns:
        True nếu đã login, False nếu chưa
    """
    try:
        current_url = page.url

        # Kiểm tra URL redirect
        if "login" in current_url or "auth" in current_url or "signin" in current_url:
            return False

        # Kiểm tra presence của chat input
        chat_input_present = await page.query_selector(sel.CHAT_INPUT_TEXTAREA_CSS)
        if chat_input_present:
            return True

        # Kiểm tra user avatar/name trong footer
        user_avatar = await page.query_selector(sel.FOOTER_USER_AVATAR_IMG_CSS)
        if user_avatar:
            return True

        return False
    except:
        return False

async def launch_browser_with_stealth_mode(
    profile_name: Optional[str] = None,
    use_system_profile: bool = False,
    force_session_preservation: bool = True
) -> tuple[Browser, BrowserContext]:
    """
    Khởi chạy browser với stealth mode tối ưu nhất để preserve session.

    Args:
        profile_name: Tên profile tùy chỉnh
        use_system_profile: Sử dụng profile Chrome hệ thống
        force_session_preservation: Force preservation với mọi giá

    Returns:
        Tuple của (playwright, context)
    """
    playwright = await async_playwright().start()

    if use_system_profile:
        user_data_dir = get_system_chrome_user_data_dir()
        if not user_data_dir or not os.path.exists(user_data_dir):
            raise ValueError("Không tìm thấy Chrome profile hệ thống")
    elif profile_name:
        user_data_dir = os.path.join(settings.CHROME_PROFILE_BASE_PATH, profile_name)
        os.makedirs(user_data_dir, exist_ok=True)
    else:
        user_data_dir = None

    # Cleanup lock files và kill processes trước khi launch
    if user_data_dir and profile_name:
        kill_chrome_processes_for_profile(profile_name)
        cleanup_profile_locks(user_data_dir)
        # Đợi lâu hơn để đảm bảo cleanup hoàn toàn
        await asyncio.sleep(3)

    # Super minimal args cho stealth mode
    chrome_args = [
        "--no-sandbox",
        "--disable-dev-shm-usage",
        "--no-first-run",
        "--disable-default-apps",
        "--no-zygote",
        "--single-process",
        "--disable-gpu",
        "--remote-debugging-port=0",
        "--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    ]

    try:
        browser = await playwright.chromium.launch_persistent_context(
            user_data_dir=user_data_dir,
            headless=True,
            args=chrome_args,
            ignore_https_errors=True,
            java_script_enabled=True,
            accept_downloads=True,
            # Thêm các options để maintain session
            bypass_csp=True,
            ignore_default_args=["--enable-automation", "--enable-blink-features=IdleDetection"],
            permissions=["notifications", "geolocation"]
        )

        # Verify session preservation
        if force_session_preservation and user_data_dir:
            # Kiểm tra các session files còn tồn tại
            session_files = [
                os.path.join(user_data_dir, "Default", "Cookies"),
                os.path.join(user_data_dir, "Default", "Local Storage"),
                os.path.join(user_data_dir, "Default", "Session Storage")
            ]

            for session_file in session_files:
                if not os.path.exists(session_file):
                    print(f"⚠️  Session file missing: {session_file}")

    except Exception as e:
        # Fallback với arguments ít hơn nữa
        if "SingletonLock" in str(e) or "ProcessSingleton" in str(e):
            if user_data_dir and profile_name:
                kill_chrome_processes_for_profile(profile_name)
                cleanup_profile_locks(user_data_dir)
                await asyncio.sleep(5)  # Đợi lâu hơn

                # Try with even more minimal args
                minimal_args = ["--no-sandbox", "--disable-dev-shm-usage"]
                browser = await playwright.chromium.launch_persistent_context(
                    user_data_dir=user_data_dir,
                    headless=True,
                    args=minimal_args
                )
            else:
                raise e
        else:
            raise e

    return playwright, browser

async def backup_session_data(profile_name: str) -> bool:
    """
    Backup session data từ profile để restore lại sau khi headless.

    Args:
        profile_name: Tên profile

    Returns:
        True nếu backup thành công
    """
    try:
        user_data_dir = os.path.join(settings.CHROME_PROFILE_BASE_PATH, profile_name)
        backup_dir = os.path.join(user_data_dir, "session_backup")

        # Tạo backup directory
        os.makedirs(backup_dir, exist_ok=True)

        # Backup các session files quan trọng
        import shutil
        session_files = [
            ("Default/Cookies", "Cookies.bak"),
            ("Default/Local Storage", "LocalStorage.bak"),
            ("Default/Session Storage", "SessionStorage.bak"),
            ("Default/IndexedDB", "IndexedDB.bak"),
            ("Default/Preferences", "Preferences.bak")
        ]

        for source, dest in session_files:
            source_path = os.path.join(user_data_dir, source)
            dest_path = os.path.join(backup_dir, dest)

            if os.path.exists(source_path):
                if os.path.isdir(source_path):
                    shutil.copytree(source_path, dest_path, dirs_exist_ok=True)
                else:
                    shutil.copy2(source_path, dest_path)

        print(f"✅ Session backup completed for {profile_name}")
        return True

    except Exception as e:
        print(f"❌ Session backup failed: {e}")
        return False

async def restore_session_data(profile_name: str) -> bool:
    """
    Restore session data từ backup.

    Args:
        profile_name: Tên profile

    Returns:
        True nếu restore thành công
    """
    try:
        user_data_dir = os.path.join(settings.CHROME_PROFILE_BASE_PATH, profile_name)
        backup_dir = os.path.join(user_data_dir, "session_backup")

        if not os.path.exists(backup_dir):
            print(f"⚠️  No backup found for {profile_name}")
            return False

        # Restore các session files
        import shutil
        session_files = [
            ("Cookies.bak", "Default/Cookies"),
            ("LocalStorage.bak", "Default/Local Storage"),
            ("SessionStorage.bak", "Default/Session Storage"),
            ("IndexedDB.bak", "Default/IndexedDB"),
            ("Preferences.bak", "Default/Preferences")
        ]

        for source, dest in session_files:
            source_path = os.path.join(backup_dir, source)
            dest_path = os.path.join(user_data_dir, dest)

            if os.path.exists(source_path):
                # Đảm bảo destination directory exists
                os.makedirs(os.path.dirname(dest_path), exist_ok=True)

                if os.path.isdir(source_path):
                    if os.path.exists(dest_path):
                        shutil.rmtree(dest_path)
                    shutil.copytree(source_path, dest_path)
                else:
                    shutil.copy2(source_path, dest_path)

        print(f"✅ Session restore completed for {profile_name}")
        return True

    except Exception as e:
        print(f"❌ Session restore failed: {e}")
        return False

async def extract_cookies_from_profile(profile_name: str) -> list:
    """
    Extract cookies từ profile để inject lại sau.

    Args:
        profile_name: Tên profile

    Returns:
        List các cookies
    """
    try:
        from playwright.async_api import async_playwright

        # Khởi chạy browser non-headless để extract cookies
        playwright = await async_playwright().start()
        user_data_dir = os.path.join(settings.CHROME_PROFILE_BASE_PATH, profile_name)

        browser = await playwright.chromium.launch_persistent_context(
            user_data_dir=user_data_dir,
            headless=False
        )

        page = await browser.new_page()
        await page.goto("https://manus.im/", wait_until='networkidle')

        # Extract cookies
        cookies = await page.context.cookies()

        await browser.close()
        await playwright.stop()

        # Lưu cookies vào file
        import json
        cookies_file = os.path.join(user_data_dir, "extracted_cookies.json")
        with open(cookies_file, 'w') as f:
            json.dump(cookies, f, indent=2)

        print(f"✅ Extracted {len(cookies)} cookies from {profile_name}")
        return cookies

    except Exception as e:
        print(f"❌ Cookie extraction failed: {e}")
        return []

async def inject_cookies_to_context(context, profile_name: str) -> bool:
    """
    Inject cookies vào context để maintain login.

    Args:
        context: Browser context
        profile_name: Tên profile

    Returns:
        True nếu inject thành công
    """
    try:
        import json
        user_data_dir = os.path.join(settings.CHROME_PROFILE_BASE_PATH, profile_name)
        cookies_file = os.path.join(user_data_dir, "extracted_cookies.json")

        if not os.path.exists(cookies_file):
            print(f"⚠️  No cookies file found for {profile_name}")
            return False

        with open(cookies_file, 'r') as f:
            cookies = json.load(f)

        # Inject cookies vào context
        await context.add_cookies(cookies)

        print(f"✅ Injected {len(cookies)} cookies to context")
        return True

    except Exception as e:
        print(f"❌ Cookie injection failed: {e}")
        return False