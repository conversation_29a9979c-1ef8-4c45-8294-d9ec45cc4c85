"""
Configuration settings for Manus Crawler
"""

import os
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Settings:
    """Application settings."""
    
    # API Settings
    ADMIN_API_KEY: str = os.getenv("ADMIN_API_KEY", "your_super_secret_key_here")
    
    # Chrome Profile Settings
    CHROME_PROFILE_BASE_PATH: str = os.getenv("CHROME_PROFILE_BASE_PATH", "./data/chrome_profiles")
    PLAYWRIGHT_PAGE_TIMEOUT: int = 30000 
    PLAYWRIGHT_NAVIGATION_TIMEOUT: int = 60000 
    MANUS_RESPONSE_TIMEOUT: int = 120
    
    # Server Settings
    HOST: str = os.getenv("HOST", "0.0.0.0")
    PORT: int = int(os.getenv("PORT", "8000"))
    
    # Application Settings
    APP_NAME: str = "Manus Crawler API"
    APP_VERSION: str = "1.0.0"
    APP_DESCRIPTION: str = "API để crawl dữ liệu từ Manus.im với Playwright và realtime updates"
    
    # Debug Settings
    DEBUG: bool = os.getenv("DEBUG", "False").lower() == "true"
    PYTHONUNBUFFERED: str = os.getenv("PYTHONUNBUFFERED", "1")
    
    # Template Settings
    TEMPLATES_DIR: str = "app/static/templates"
    
    def __init__(self):
        """Initialize settings and create necessary directories."""
        # Ensure chrome profiles directory exists
        Path(self.CHROME_PROFILE_BASE_PATH).mkdir(parents=True, exist_ok=True)

# Global settings instance
settings = Settings()
