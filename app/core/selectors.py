# app/core/selectors.py
# Unified selector system - loads from JSON and provides both old and new style access

import json
import re
from types import SimpleNamespace
from pathlib import Path
from typing import Union, Optional

# Path to selectors.json file (same directory as this file)
_SELECTOR_FILE_PATH = Path(__file__).parent / "selectors.json"


def _camel_to_snake(name: str) -> str:
    """Converts a camelCase string to snake_case."""
    name = re.sub(r'(.)([A-Z][a-z]+)', r'\1_\2', name)
    return re.sub(r'([a-z0-9])([A-Z])', r'\1_\2', name).lower()


class _SelectorGroup(SimpleNamespace):
    """
    Represents a group of selectors, allowing attribute-style access.
    Converts camelCase keys from JSON to snake_case attributes.
    """
    def __init__(self, **kwargs):
        super().__init__(**{_camel_to_snake(k): v for k, v in kwargs.items()})

    def __repr__(self):
        attrs = ", ".join(f"{k}='{v}'" for k, v in sorted(self.__dict__.items()))
        return f"<{self.__class__.__name__}: {attrs}>"


class CrawlSelectors:
    """
    Loads selectors from JSON and provides access via attribute groups.

    Usage:
        # New style (recommended)
        SELECTORS.sidebar.task_item_container_css

        # Old style (backward compatibility)
        TASK_ITEM_CONTAINER_CSS
    """

    # Type hints for better autocompletion
    general_page_elements: _SelectorGroup
    sidebar: _SelectorGroup
    main_content_area: _SelectorGroup
    footer_sidebar: _SelectorGroup
    computer_preview_area: _SelectorGroup
    input_area: _SelectorGroup
    login_page: _SelectorGroup

    def __init__(self, file_path: Union[str, Path] = _SELECTOR_FILE_PATH):
        self._file_path = Path(file_path)
        self._load_selectors()

    def _load_selectors(self):
        """Loads selectors from the JSON file and populates the instance."""
        try:
            with open(self._file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except FileNotFoundError:
            raise FileNotFoundError(f"Selector file not found: {self._file_path.resolve()}")
        except json.JSONDecodeError as e:
            raise ValueError(f"Error decoding JSON from {self._file_path.resolve()}: {e}")

        for group_key_camel, group_data in data.items():
            group_key_snake = _camel_to_snake(group_key_camel)
            if isinstance(group_data, dict):
                setattr(self, group_key_snake, _SelectorGroup(**group_data))

    def get_group(self, group_name: str) -> Optional[_SelectorGroup]:
        """Retrieves a selector group by its snake_case name."""
        return getattr(self, group_name, None)

    def __repr__(self):
        groups = [k for k in dir(self) if not k.startswith('_') and isinstance(getattr(self, k), _SelectorGroup)]
        return f"<CrawlSelectors: {', '.join(sorted(groups))}>"


# Create the global selector instance
SELECTORS = CrawlSelectors()

# === BACKWARD COMPATIBILITY CONSTANTS ===
# Old style constants that map to the new system

# General Page Elements
PAGE_TITLE_TAG = SELECTORS.general_page_elements.page_title_tag

# Sidebar selectors
TASK_ITEM_CONTAINER_CSS = SELECTORS.sidebar.task_item_container_css
TASK_ITEM_ICON_IMG_CSS = SELECTORS.sidebar.task_item_icon_img_css
TASK_ITEM_TITLE_SPAN_CSS = SELECTORS.sidebar.task_item_title_span_css
TASK_ITEM_TIMESTAMP_CSS = SELECTORS.sidebar.task_item_timestamp_css
TASK_ITEM_PREVIEW_SPAN_CSS = SELECTORS.sidebar.task_item_preview_span_css
NEW_TASK_BUTTON_PLAYWRIGHT = SELECTORS.sidebar.new_task_button_playwright

# Main content area selectors
CURRENT_TASK_TITLE_MAIN_CSS = SELECTORS.main_content_area.current_task_title_main_css
CHAT_EVENT_CONTAINER_CSS = SELECTORS.main_content_area.chat_event_container_css
USER_MESSAGE_TEXT_CSS = SELECTORS.main_content_area.user_message_text_css
MANUS_MESSAGE_CONTENT_PROSE_CSS = SELECTORS.main_content_area.manus_message_content_prose_css
MESSAGE_TIMESTAMP_CSS = SELECTORS.main_content_area.message_timestamp_css
ATTACHMENT_IN_MESSAGE_CONTAINER_CSS = SELECTORS.main_content_area.attachment_in_message_container_css
ATTACHMENT_IN_MESSAGE_FILENAME_CSS = SELECTORS.main_content_area.attachment_in_message_filename_css
ATTACHMENT_IN_MESSAGE_DETAILS_CSS = SELECTORS.main_content_area.attachment_in_message_details_css
ATTACH_ALL_FILE_IN_MESSAGE_CSS = SELECTORS.main_content_area.attach_all_file_in_message_css
VIEW_ALL_FILE_IN_MESSAGE_CSS = SELECTORS.main_content_area.view_all_file_in_message_css
DOWNLOAD_BUTTON_CSS = SELECTORS.main_content_area.download_button_css
BATCH_DOWNLOAD_BUTTON_CSS = SELECTORS.main_content_area.batch_download_button_css

# Footer sidebar selectors
FOOTER_USER_AVATAR_IMG_CSS = SELECTORS.footer_sidebar.user_avatar_img_css
FOOTER_USER_NAME_SPAN_CSS = SELECTORS.footer_sidebar.user_name_span_css

# Computer preview area selectors
COMPUTER_PREVIEW_CONTAINER_ID = SELECTORS.computer_preview_area.container_id
COMPUTER_PREVIEW_FILENAME_CSS = SELECTORS.computer_preview_area.filename_css
COMPUTER_PREVIEW_MONACO_LINES_CSS = SELECTORS.computer_preview_area.monaco_lines_css

# Input area selectors
CHAT_INPUT_TEXTAREA_CSS = SELECTORS.input_area.chat_input_textarea_css

# Login page selectors
SIGN_IN_WITH_GOOGLE_BUTTON_XPATH = SELECTORS.login_page.sign_in_with_google_button_x_path
SIGN_IN_WITH_GOOGLE_BUTTON_CSS = SELECTORS.login_page.sign_in_with_google_button_css
GOOGLE_EMAIL_INPUT_SELECTOR = SELECTORS.login_page.google_email_input_selector
GOOGLE_PASSWORD_INPUT_SELECTOR = SELECTORS.login_page.google_password_input_selector
GOOGLE_NEXT_BUTTON_SELECTOR = SELECTORS.login_page.google_next_button_selector

# Status check selectors
MAKE_COMPLETED_CSS = SELECTORS.status.make_completed_css
COPY_BUTTON_CSS = SELECTORS.status.copy_button_css


# === TESTING (can be run directly: python -m app.core.selectors) ===
if __name__ == "__main__":
    print("=== TESTING OPTIMIZED SELECTOR SYSTEM ===")

    # Test new style
    print(f"\n✓ New style access:")
    print(f"  Sidebar container: {SELECTORS.sidebar.task_item_container_css[:50]}...")
    print(f"  Login XPath: {SELECTORS.login_page.sign_in_with_google_button_x_path}")

    # Test old style
    print(f"\n✓ Old style access:")
    print(f"  Sidebar container: {TASK_ITEM_CONTAINER_CSS[:50]}...")
    print(f"  Login XPath: {SIGN_IN_WITH_GOOGLE_BUTTON_XPATH}")

    # Test equality
    print(f"\n✓ Compatibility check:")
    print(f"  Equal: {TASK_ITEM_CONTAINER_CSS == SELECTORS.sidebar.task_item_container_css}")

    # Show loaded groups
    print(f"\n✓ Loaded groups: {SELECTORS}")

    print(f"\n🎉 OPTIMIZED SYSTEM WORKS PERFECTLY!")
    print(f"📁 Files: selectors.py + selectors.json (2 files total)")
