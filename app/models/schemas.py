"""
Pydantic models for request/response schemas
"""

from typing import Optional, Dict, Any, List
from pydantic import BaseModel

# Task and Chat Models
class TaskItem(BaseModel):
    icon_src: Optional[str] = None
    title: Optional[str] = None
    title_text: Optional[str] = None
    timestamp: Optional[str] = None
    preview: Optional[str] = None

class ChatMessage(BaseModel):
    event_id: Optional[str] = None
    type: Optional[str] = None
    user_message: Optional[str] = None
    manus_message: Optional[str] = None
    manus_html: Optional[str] = None
    timestamp: Optional[str] = None
    # Thêm các field mới cho phân loại message
    message_subtype: Optional[str] = None  # "text", "code", "file", "list", "mixed"
    content_analysis: Optional[Dict[str, Any]] = None  # Chi tiết phân tích nội dung

class FooterUserInfo(BaseModel):
    avatar_src: Optional[str] = None
    name: Optional[str] = None

class ProfileStatus(BaseModel):
    profile_name: Optional[str] = None
    use_system_profile: bool = False
    headless: bool = True

class CrawledDataResponse(BaseModel):
    page_title: str = ""
    tasks: List[TaskItem] = []
    current_task_title: str = ""
    chat_messages: List[ChatMessage] = []
    footer_user: FooterUserInfo = FooterUserInfo()
    profile_status: ProfileStatus = ProfileStatus()

# Request Models
class CrawlUrlRequest(BaseModel):
    url: str
    profile_name: Optional[str] = None
    use_system_profile: bool = False
    headless: bool = True

class CrawlUrlRequestWithId(CrawlUrlRequest):
    request_id: str

class CrawlHtmlRequest(BaseModel):
    html_content: str
    profile_name: Optional[str] = None
    use_system_profile: bool = False
    headless: bool = True

class CrawlHtmlRequestWithId(CrawlHtmlRequest):
    request_id: str

class SetupProfileRequest(BaseModel):
    profile_name: str
    url: str = "https://manus.im/"

# WebSocket Models
class WebSocketMessage(BaseModel):
    type: str  # "progress", "data", "error"
    message: Optional[str] = None
    data: Optional[Dict[str, Any]] = None

# Response Models
class StandardResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None

class HealthResponse(BaseModel):
    status: str
    message: str

class ProfileListResponse(BaseModel):
    success: bool
    profiles: List[Dict[str, Any]] = []
    total: int = 0
    base_path: Optional[str] = None
    error: Optional[str] = None

# Interactive Chat Models
class ChatWithManusRequest(BaseModel):
    message: str
    task_url: str
    profile_name: Optional[str] = None
    request_id: str
    headless: bool = True
    user_id: Optional[str] = None  # Thêm user_id để track user

class ChatResponse(BaseModel):
    user_message: str
    manus_response: str
    timestamp: str
    attachments: List[Dict[str, Any]] = []

class InteractiveChatResult(BaseModel):
    success: bool
    chat_response: Optional[ChatResponse] = None
    updated_page_data: Optional[CrawledDataResponse] = None
    timestamp: str
    error: Optional[str] = None

# Status Check Models
class CheckCrawlStatusRequest(BaseModel):
    task_id: str
    profile_name: Optional[str] = None
    headless: bool = True

class CheckCrawlStatusResponse(BaseModel):
    success: bool
    task_id: str
    status: str
    message: Optional[str] = None
    last_updated: Optional[str] = None
    error: Optional[str] = None
    uploaded_files: Optional[List[Dict[str, Any]]] = None  # Thêm danh sách file upload
    detection_method: Optional[str] = None  # "completion_message", "copy_button", "none"

# Thêm schema mới cho response file list
class FileInfo(BaseModel):
    filename: str
    # S3 fields (thay thế Cloudinary)
    s3_url: Optional[str] = None
    s3_bucket: Optional[str] = None
    s3_key: Optional[str] = None
    content_type: Optional[str] = None
    file_size: Optional[int] = None
    # Backwards compatibility với Cloudinary (có thể xóa sau)
    cloudinary_url: Optional[str] = None
    public_id: Optional[str] = None
    resource_type: Optional[str] = None
    format: Optional[str] = None
    bytes: Optional[int] = None
    upload_time: Optional[str] = None
    uploaded_at: Optional[str] = None
    local_path: Optional[str] = None
    upload_error: Optional[str] = None
    # Thêm fields cho zip extraction
    type: Optional[str] = None  # "regular_file", "zip_extracted", "zip_original", "zip_summary"
    extracted_from_zip: Optional[str] = None  # Tên file zip gốc
    relative_path_in_zip: Optional[str] = None  # Đường dẫn trong zip
    subfolder: Optional[str] = None  # Subfolder trên S3
    total_extracted: Optional[int] = None  # Cho zip_summary
    extracted_files: Optional[List[Dict[str, Any]]] = None  # Cho zip_summary
    task_id: Optional[str] = None  # ID của task

class GetUploadedFilesRequest(BaseModel):
    task_id: str

class GetUploadedFilesResponse(BaseModel):
    success: bool
    task_id: str
    files: List[FileInfo] = []
    total_files: int = 0
    task_status: Optional[str] = None
    error: Optional[str] = None

# S3 Presigned URL schemas
class GetPresignedUrlRequest(BaseModel):
    key: str
    content_type: str
    task_id: str

class GetPresignedUrlResponse(BaseModel):
    success: bool
    presigned_url: Optional[str] = None
    fields: Optional[Dict[str, str]] = None
    error: Optional[str] = None

# S3 Direct Upload schemas
class S3UploadRequest(BaseModel):
    task_id: str
    subfolder: Optional[str] = None

class S3UploadResponse(BaseModel):
    success: bool
    filename: Optional[str] = None
    s3_url: Optional[str] = None
    s3_bucket: Optional[str] = None
    s3_key: Optional[str] = None
    file_size: Optional[int] = None
    content_type: Optional[str] = None
    upload_time: Optional[str] = None
    message: Optional[str] = None
    error: Optional[str] = None


class OpenProfileRequest(BaseModel):
    profile_name: str