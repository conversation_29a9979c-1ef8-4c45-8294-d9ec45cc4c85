"""
Test script để kiểm tra sự khác biệt giữa headless và non-headless mode
với việc preserve session trong Manus.
"""

import asyncio
import sys
import os

# Thêm project root vào Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.core.crawler import chat_with_manus_interactive

async def test_headless_vs_non_headless():
    """
    Test sự khác biệt giữa headless và non-headless mode.
    """
    
    # Cấu hình test
    test_message = "Hello, can you help me with a simple Python question?"
    test_task_url = "https://manus.im/app/670c1e4e2ce59c7c5bb9bb23"  # Thay bằng task URL thật
    test_profile = "manus_login_profile"
    
    print("🧪 Testing Manus Chat Session Preservation")
    print("=" * 60)
    
    # Test 1: Non-headless mode
    print("\n📋 Test 1: Non-headless mode")
    print("-" * 30)
    
    try:
        result_non_headless = await chat_with_manus_interactive(
            message=test_message,
            task_url=test_task_url,
            profile_name=test_profile,
            headless=False
        )
        
        print("✅ Non-headless test completed")
        if result_non_headless.get("success"):
            session_status = result_non_headless.get("session_status", {})
            print(f"   - Initially logged in: {session_status.get('initially_logged_in')}")
            print(f"   - Still logged in after chat: {session_status.get('still_logged_in_after_chat')}")
            print(f"   - Profile used: {session_status.get('profile_used')}")
            print(f"   - Response received: {bool(result_non_headless.get('chat_response'))}")
        else:
            print(f"   - Error: {result_non_headless.get('error')}")
            
    except Exception as e:
        print(f"❌ Non-headless test failed: {e}")
    
    # Đợi một chút giữa các test
    print("\n⏳ Waiting 5 seconds before next test...")
    await asyncio.sleep(5)
    
    # Test 2: Headless mode
    print("\n📋 Test 2: Headless mode")
    print("-" * 30)
    
    try:
        result_headless = await chat_with_manus_interactive(
            message=test_message,
            task_url=test_task_url,
            profile_name=test_profile,
            headless=True
        )
        
        print("✅ Headless test completed")
        if result_headless.get("success"):
            session_status = result_headless.get("session_status", {})
            print(f"   - Initially logged in: {session_status.get('initially_logged_in')}")
            print(f"   - Still logged in after chat: {session_status.get('still_logged_in_after_chat')}")
            print(f"   - Profile used: {session_status.get('profile_used')}")
            print(f"   - Response received: {bool(result_headless.get('chat_response'))}")
        else:
            print(f"   - Error: {result_headless.get('error')}")
            
    except Exception as e:
        print(f"❌ Headless test failed: {e}")
    
    # So sánh kết quả
    print("\n📊 Comparison Results")
    print("=" * 60)
    
    if 'result_non_headless' in locals() and 'result_headless' in locals():
        non_headless_success = result_non_headless.get("success", False)
        headless_success = result_headless.get("success", False)
        
        print(f"Non-headless success: {non_headless_success}")
        print(f"Headless success: {headless_success}")
        
        if non_headless_success and headless_success:
            nh_session = result_non_headless.get("session_status", {})
            h_session = result_headless.get("session_status", {})
            
            print(f"\nSession preservation comparison:")
            print(f"Non-headless still logged in: {nh_session.get('still_logged_in_after_chat')}")
            print(f"Headless still logged in: {h_session.get('still_logged_in_after_chat')}")
            
            if nh_session.get('still_logged_in_after_chat') and not h_session.get('still_logged_in_after_chat'):
                print("🚨 ISSUE DETECTED: Headless mode caused logout!")
            elif h_session.get('still_logged_in_after_chat'):
                print("✅ SUCCESS: Headless mode preserved session!")
            else:
                print("⚠️  Both modes had session issues")
        
    print("\n🎯 Test completed!")

if __name__ == "__main__":
    # Chạy test
    print("Starting Manus session preservation test...")
    print("Make sure you have already set up 'manus_login_profile' profile!")
    
    # Cho user 5 giây để cancel nếu chưa setup profile
    import time
    for i in range(5, 0, -1):
        print(f"Starting in {i} seconds... (Ctrl+C to cancel)")
        time.sleep(1)
    
    asyncio.run(test_headless_vs_non_headless()) 