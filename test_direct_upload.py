#!/usr/bin/env python3
"""
Test script để upload tr<PERSON><PERSON> tiế<PERSON> sử dụng S3Uploader
"""

import asyncio
import tempfile
import os
from app.utils.s3_upload import s3_uploader

async def test_direct_s3_upload():
    """Test upload trực tiếp với S3Uploader"""
    
    # Tạo file test
    test_content = "Test upload trực tiếp với tiếng Việt: Báo cáo giá vàng hôm nay 📊"
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
        f.write(test_content)
        temp_file_path = f.name
    
    try:
        print(f"📄 Tạo file test: {temp_file_path}")
        
        # Test upload file
        print("\n🧪 Test 1: Upload file với subfolder tiếng Việt")
        result1 = await s3_uploader.upload_file(
            file_path=temp_file_path,
            task_id="test_direct_upload",
            subfolder="Báo cáo/Giá vàng/Hôm nay"
        )
        
        if result1:
            print(f"   ✅ Upload thành công!")
            print(f"   📍 S3 URL: {result1['s3_url']}")
            print(f"   🔑 S3 Key: {result1['s3_key']}")
            print(f"   📁 Subfolder: {result1.get('subfolder', 'N/A')}")
        else:
            print(f"   ❌ Upload thất bại!")
        
        # Test upload bytes
        print("\n🧪 Test 2: Upload bytes với subfolder tiếng Việt")
        test_bytes = "Nội dung từ bytes với ký tự đặc biệt: àáạảãâầấậẩẫăằắặẳẵ".encode('utf-8')
        
        result2 = await s3_uploader.upload_file_bytes(
            file_bytes=test_bytes,
            filename="test-bytes-vietnamese.txt",
            task_id="test_bytes_direct",
            subfolder="Thử nghiệm/Upload bytes",
            content_type="text/plain"
        )
        
        if result2:
            print(f"   ✅ Upload bytes thành công!")
            print(f"   📍 S3 URL: {result2['s3_url']}")
            print(f"   🔑 S3 Key: {result2['s3_key']}")
            print(f"   📁 Subfolder: {result2.get('subfolder', 'N/A')}")
        else:
            print(f"   ❌ Upload bytes thất bại!")
            
        # Test list files
        print("\n🧪 Test 3: List files")
        files = await s3_uploader.list_files("test_direct_upload")
        print(f"   📋 Tìm thấy {len(files)} files:")
        for file_info in files:
            print(f"      - {file_info['key']} ({file_info['size']} bytes)")
    
    finally:
        # Dọn dẹp
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)
            print(f"\n🧹 Đã xóa file test: {temp_file_path}")

def main():
    """Main function"""
    print("🚀 Test upload trực tiếp với S3Uploader")
    print("=" * 60)
    
    # Kiểm tra S3 có sẵn sàng không
    if not s3_uploader._check_s3_available():
        print("❌ S3 client không sẵn sàng. Kiểm tra cấu hình AWS credentials.")
        return
    
    print(f"✅ S3 client sẵn sàng. Bucket: {s3_uploader.bucket_name}")
    
    # Chạy test
    asyncio.run(test_direct_s3_upload())
    
    print("\n" + "=" * 60)
    print("🎉 Hoàn thành test upload trực tiếp!")

if __name__ == "__main__":
    main() 