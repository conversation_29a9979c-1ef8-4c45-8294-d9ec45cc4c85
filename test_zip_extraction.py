#!/usr/bin/env python3
"""
Test script để demo chức năng zip extraction và upload lên Cloudinary
"""

import asyncio
import os
import tempfile
import zipfile
from pathlib import Path

# Import từ ứng dụng
from app.utils.cloudinary_upload import cloudinary_uploader

async def create_test_zip() -> str:
    """
    Tạo một file zip test với các file khác nhau
    """
    # Tạo thư mục tạm
    with tempfile.TemporaryDirectory() as temp_dir:
        # Tạo các file test
        files = {
            "readme.txt": "This is a readme file for testing zip extraction",
            "config.json": '{"name": "test", "version": "1.0.0", "debug": true}',
            "script.py": "#!/usr/bin/env python3\nprint('Hello from extracted Python file!')",
            "subfolder/data.csv": "name,age,city\nJohn,25,New York\nJane,30,San Francisco",
            "subfolder/nested/deep.md": "# Deep File\nThis file is nested deep in the zip structure."
        }
        
        # T<PERSON><PERSON> c<PERSON>u trúc thư mục và file
        for file_path, content in files.items():
            full_path = os.path.join(temp_dir, file_path)
            os.makedirs(os.path.dirname(full_path), exist_ok=True)
            
            with open(full_path, 'w', encoding='utf-8') as f:
                f.write(content)
        
        # Tạo file zip
        zip_path = "test_files.zip"
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files_list in os.walk(temp_dir):
                for file in files_list:
                    file_path = os.path.join(root, file)
                    arcname = os.path.relpath(file_path, temp_dir)
                    zipf.write(file_path, arcname)
        
        print(f"✅ Created test zip file: {zip_path}")
        return zip_path

async def test_zip_extraction():
    """
    Test chức năng extract và upload zip
    """
    print("🧪 Testing ZIP extraction and upload to Cloudinary...")
    
    # Tạo test zip
    zip_path = await create_test_zip()
    
    try:
        # Test task ID
        test_task_id = "test_zip_extraction_task"
        
        # Test chức năng is_zip_file
        print(f"\n🔍 Testing zip detection...")
        is_zip = cloudinary_uploader.is_zip_file(zip_path)
        print(f"Is zip file: {is_zip}")
        
        if not is_zip:
            print("❌ Zip detection failed!")
            return
        
        # Test extract và upload
        print(f"\n📦 Testing extract and upload...")
        extracted_files = await cloudinary_uploader.extract_and_upload_zip(zip_path, test_task_id)
        
        if extracted_files:
            print(f"\n✅ Successfully extracted and uploaded {len(extracted_files)} files:")
            for i, file_info in enumerate(extracted_files, 1):
                print(f"  {i}. {file_info['filename']}")
                print(f"     📁 Subfolder: {file_info.get('subfolder', 'None')}")
                print(f"     🔗 URL: {file_info.get('cloudinary_url', 'N/A')}")
                print(f"     📂 Original zip: {file_info.get('extracted_from_zip', 'N/A')}")
                print(f"     📍 Path in zip: {file_info.get('relative_path_in_zip', 'N/A')}")
                print()
        else:
            print("❌ No files were extracted and uploaded!")
            
    except Exception as e:
        print(f"❌ Error during test: {str(e)}")
        
    finally:
        # Dọn dẹp
        if os.path.exists(zip_path):
            os.remove(zip_path)
            print(f"🧹 Cleaned up test zip file: {zip_path}")

async def test_upload_single_file():
    """
    Test upload một file thường để so sánh
    """
    print("\n🧪 Testing single file upload for comparison...")
    
    # Tạo file test
    test_file = "test_single_file.txt"
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write("This is a test file for single upload.")
    
    try:
        # Upload file
        result = await cloudinary_uploader.upload_file(test_file, "test_single_upload")
        
        if result:
            print(f"✅ Single file upload successful:")
            print(f"   📄 Filename: {result['filename']}")
            print(f"   🔗 URL: {result['cloudinary_url']}")
            print(f"   📁 Subfolder: {result.get('subfolder', 'None')}")
        else:
            print("❌ Single file upload failed!")
            
    except Exception as e:
        print(f"❌ Error during single file test: {str(e)}")
        
    finally:
        # Dọn dẹp
        if os.path.exists(test_file):
            os.remove(test_file)
            print(f"🧹 Cleaned up test file: {test_file}")

async def main():
    """
    Main test function
    """
    print("🚀 Starting ZIP extraction tests...")
    print("=" * 60)
    
    # Test 1: Single file upload
    await test_upload_single_file()
    
    print("\n" + "=" * 60)
    
    # Test 2: ZIP extraction
    await test_zip_extraction()
    
    print("\n" + "=" * 60)
    print("✅ All tests completed!")

if __name__ == "__main__":
    asyncio.run(main()) 